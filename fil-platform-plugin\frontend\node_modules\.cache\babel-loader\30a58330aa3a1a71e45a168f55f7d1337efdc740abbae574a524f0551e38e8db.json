{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Container,Row,Col,Card,Button}from'react-bootstrap';import{useTranslation}from'react-i18next';import{Link}from'react-router-dom';import{getSupabase}from'../../supabaseClient';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const AgentDashboard=()=>{const{t}=useTranslation();const[agentProfile,setAgentProfile]=useState(null);const[loading,setLoading]=useState(true);useEffect(()=>{const fetchAgentData=async()=>{const supabase=getSupabase();if(!supabase)return;setLoading(true);const{data:{user}}=await supabase.auth.getUser();if(!user){console.log('No user found');setLoading(false);return;// User not logged in\n}console.log('Current user:',user.id);console.log('User role from localStorage:',localStorage.getItem('user_role'));// Fetch agent profile\nconst{data:profileData,error:profileError}=await supabase.from('agent_profiles').select('*, maker_profiles(brand_name)').eq('user_id',user.id).single();if(profileError){console.error('Error fetching agent profile:',profileError);console.error('User ID:',user.id);console.error('Error details:',profileError);}else{console.log('Agent profile data:',profileData);setAgentProfile(profileData);}setLoading(false);};fetchAgentData();},[]);if(loading){return/*#__PURE__*/_jsx(\"div\",{children:t('loading_agent_dashboard')});}if(!agentProfile){return/*#__PURE__*/_jsx(\"div\",{className:\"alert alert-warning\",children:t('not_agent')});}return/*#__PURE__*/_jsxs(Container,{fluid:true,children:[/*#__PURE__*/_jsx(Row,{className:\"mb-3\",children:/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(\"h2\",{children:t('agent_dashboard')})})}),/*#__PURE__*/_jsxs(Row,{children:[/*#__PURE__*/_jsx(Col,{md:4,children:/*#__PURE__*/_jsx(Card,{className:\"bg-primary text-white mb-3\",children:/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsx(Card.Title,{children:t('brand_name')}),/*#__PURE__*/_jsx(\"h3\",{children:agentProfile.brand_name||'N/A'})]})})}),/*#__PURE__*/_jsx(Col,{md:4,children:/*#__PURE__*/_jsx(Card,{className:\"bg-success text-white mb-3\",children:/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsx(Card.Title,{children:t('commission_rate')}),/*#__PURE__*/_jsx(\"h3\",{children:agentProfile.commission_pct?`${agentProfile.commission_pct*100}%`:'N/A'})]})})}),/*#__PURE__*/_jsx(Col,{md:4,children:/*#__PURE__*/_jsx(Card,{className:\"bg-info text-white mb-3\",children:/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsx(Card.Title,{children:t('kyc_status')}),/*#__PURE__*/_jsx(\"h3\",{children:agentProfile.kyc_status||'N/A'})]})})})]}),/*#__PURE__*/_jsxs(Row,{className:\"mt-4\",children:[/*#__PURE__*/_jsx(Col,{md:6,className:\"text-center\",children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsx(\"h4\",{children:t('member_management')}),/*#__PURE__*/_jsx(\"p\",{children:t('my_subordinate_members')}),/*#__PURE__*/_jsx(Button,{as:Link,to:\"/agent/members\",variant:\"primary\",children:t('enter_member_list')})]})})}),/*#__PURE__*/_jsx(Col,{md:6,className:\"text-center\",children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsx(\"h4\",{children:t('product_management')}),/*#__PURE__*/_jsx(\"p\",{children:t('products_on_sale')}),/*#__PURE__*/_jsx(Button,{as:Link,to:\"/agent/products\",variant:\"success\",children:t('browse_agent_products')})]})})})]})]});};export default AgentDashboard;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "Card", "<PERSON><PERSON>", "useTranslation", "Link", "getSupabase", "jsx", "_jsx", "jsxs", "_jsxs", "AgentDashboard", "t", "agentProfile", "setAgentProfile", "loading", "setLoading", "fetchAgentData", "supabase", "data", "user", "auth", "getUser", "console", "log", "id", "localStorage", "getItem", "profileData", "error", "profileError", "from", "select", "eq", "single", "children", "className", "fluid", "md", "Body", "Title", "brand_name", "commission_pct", "kyc_status", "as", "to", "variant"], "sources": ["D:/New_System/fil-platform-plugin/frontend/src/pages/agent/Dashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Button } from 'react-bootstrap';\nimport { useTranslation } from 'react-i18next';\nimport { Link } from 'react-router-dom';\nimport { getSupabase } from '../../supabaseClient';\n\nconst AgentDashboard = () => {\n    const { t } = useTranslation();\n    const [agentProfile, setAgentProfile] = useState(null);\n    const [loading, setLoading] = useState(true);\n\n    useEffect(() => {\n        const fetchAgentData = async () => {\n            const supabase = getSupabase();\n            if (!supabase) return;\n\n            setLoading(true);\n            const { data: { user } } = await supabase.auth.getUser();\n\n            if (!user) {\n                console.log('No user found');\n                setLoading(false);\n                return; // User not logged in\n            }\n\n            console.log('Current user:', user.id);\n            console.log('User role from localStorage:', localStorage.getItem('user_role'));\n\n            // Fetch agent profile\n            const { data: profileData, error: profileError } = await supabase\n                .from('agent_profiles')\n                .select('*, maker_profiles(brand_name)')\n                .eq('user_id', user.id)\n                .single();\n\n            if (profileError) {\n                console.error('Error fetching agent profile:', profileError);\n                console.error('User ID:', user.id);\n                console.error('Error details:', profileError);\n            } else {\n                console.log('Agent profile data:', profileData);\n                setAgentProfile(profileData);\n            }\n            setLoading(false);\n        };\n\n        fetchAgentData();\n    }, []);\n\n    if (loading) {\n        return <div>{t('loading_agent_dashboard')}</div>;\n    }\n\n    if (!agentProfile) {\n        return <div className=\"alert alert-warning\">{t('not_agent')}</div>;\n    }\n\n    return (\n        <Container fluid>\n            <Row className=\"mb-3\">\n                <Col>\n                    <h2>{t('agent_dashboard')}</h2>\n                </Col>\n            </Row>\n\n            <Row>\n                <Col md={4}>\n                    <Card className=\"bg-primary text-white mb-3\">\n                        <Card.Body>\n                            <Card.Title>{t('brand_name')}</Card.Title>\n                            <h3>{agentProfile.brand_name || 'N/A'}</h3>\n                        </Card.Body>\n                    </Card>\n                </Col>\n                <Col md={4}>\n                    <Card className=\"bg-success text-white mb-3\">\n                        <Card.Body>\n                            <Card.Title>{t('commission_rate')}</Card.Title>\n                            <h3>{agentProfile.commission_pct ? `${agentProfile.commission_pct * 100}%` : 'N/A'}</h3>\n                        </Card.Body>\n                    </Card>\n                </Col>\n                <Col md={4}>\n                    <Card className=\"bg-info text-white mb-3\">\n                        <Card.Body>\n                            <Card.Title>{t('kyc_status')}</Card.Title>\n                            <h3>{agentProfile.kyc_status || 'N/A'}</h3>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n\n            <Row className=\"mt-4\">\n                <Col md={6} className=\"text-center\">\n                    <Card>\n                        <Card.Body>\n                            <h4>{t('member_management')}</h4>\n                            <p>{t('my_subordinate_members')}</p>\n                            <Button as={Link} to=\"/agent/members\" variant=\"primary\">{t('enter_member_list')}</Button>\n                        </Card.Body>\n                    </Card>\n                </Col>\n                <Col md={6} className=\"text-center\">\n                    <Card>\n                        <Card.Body>\n                            <h4>{t('product_management')}</h4>\n                            <p>{t('products_on_sale')}</p>\n                            <Button as={Link} to=\"/agent/products\" variant=\"success\">{t('browse_agent_products')}</Button>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n        </Container>\n    );\n};\n\nexport default AgentDashboard;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,SAAS,CAAEC,GAAG,CAAEC,GAAG,CAAEC,IAAI,CAAEC,MAAM,KAAQ,iBAAiB,CACnE,OAASC,cAAc,KAAQ,eAAe,CAC9C,OAASC,IAAI,KAAQ,kBAAkB,CACvC,OAASC,WAAW,KAAQ,sBAAsB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEnD,KAAM,CAAAC,cAAc,CAAGA,CAAA,GAAM,CACzB,KAAM,CAAEC,CAAE,CAAC,CAAGR,cAAc,CAAC,CAAC,CAC9B,KAAM,CAACS,YAAY,CAAEC,eAAe,CAAC,CAAGjB,QAAQ,CAAC,IAAI,CAAC,CACtD,KAAM,CAACkB,OAAO,CAAEC,UAAU,CAAC,CAAGnB,QAAQ,CAAC,IAAI,CAAC,CAE5CC,SAAS,CAAC,IAAM,CACZ,KAAM,CAAAmB,cAAc,CAAG,KAAAA,CAAA,GAAY,CAC/B,KAAM,CAAAC,QAAQ,CAAGZ,WAAW,CAAC,CAAC,CAC9B,GAAI,CAACY,QAAQ,CAAE,OAEfF,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAEG,IAAI,CAAE,CAAEC,IAAK,CAAE,CAAC,CAAG,KAAM,CAAAF,QAAQ,CAACG,IAAI,CAACC,OAAO,CAAC,CAAC,CAExD,GAAI,CAACF,IAAI,CAAE,CACPG,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC,CAC5BR,UAAU,CAAC,KAAK,CAAC,CACjB,OAAQ;AACZ,CAEAO,OAAO,CAACC,GAAG,CAAC,eAAe,CAAEJ,IAAI,CAACK,EAAE,CAAC,CACrCF,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAEE,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC,CAAC,CAE9E;AACA,KAAM,CAAER,IAAI,CAAES,WAAW,CAAEC,KAAK,CAAEC,YAAa,CAAC,CAAG,KAAM,CAAAZ,QAAQ,CAC5Da,IAAI,CAAC,gBAAgB,CAAC,CACtBC,MAAM,CAAC,+BAA+B,CAAC,CACvCC,EAAE,CAAC,SAAS,CAAEb,IAAI,CAACK,EAAE,CAAC,CACtBS,MAAM,CAAC,CAAC,CAEb,GAAIJ,YAAY,CAAE,CACdP,OAAO,CAACM,KAAK,CAAC,+BAA+B,CAAEC,YAAY,CAAC,CAC5DP,OAAO,CAACM,KAAK,CAAC,UAAU,CAAET,IAAI,CAACK,EAAE,CAAC,CAClCF,OAAO,CAACM,KAAK,CAAC,gBAAgB,CAAEC,YAAY,CAAC,CACjD,CAAC,IAAM,CACHP,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAEI,WAAW,CAAC,CAC/Cd,eAAe,CAACc,WAAW,CAAC,CAChC,CACAZ,UAAU,CAAC,KAAK,CAAC,CACrB,CAAC,CAEDC,cAAc,CAAC,CAAC,CACpB,CAAC,CAAE,EAAE,CAAC,CAEN,GAAIF,OAAO,CAAE,CACT,mBAAOP,IAAA,QAAA2B,QAAA,CAAMvB,CAAC,CAAC,yBAAyB,CAAC,CAAM,CAAC,CACpD,CAEA,GAAI,CAACC,YAAY,CAAE,CACf,mBAAOL,IAAA,QAAK4B,SAAS,CAAC,qBAAqB,CAAAD,QAAA,CAAEvB,CAAC,CAAC,WAAW,CAAC,CAAM,CAAC,CACtE,CAEA,mBACIF,KAAA,CAACX,SAAS,EAACsC,KAAK,MAAAF,QAAA,eACZ3B,IAAA,CAACR,GAAG,EAACoC,SAAS,CAAC,MAAM,CAAAD,QAAA,cACjB3B,IAAA,CAACP,GAAG,EAAAkC,QAAA,cACA3B,IAAA,OAAA2B,QAAA,CAAKvB,CAAC,CAAC,iBAAiB,CAAC,CAAK,CAAC,CAC9B,CAAC,CACL,CAAC,cAENF,KAAA,CAACV,GAAG,EAAAmC,QAAA,eACA3B,IAAA,CAACP,GAAG,EAACqC,EAAE,CAAE,CAAE,CAAAH,QAAA,cACP3B,IAAA,CAACN,IAAI,EAACkC,SAAS,CAAC,4BAA4B,CAAAD,QAAA,cACxCzB,KAAA,CAACR,IAAI,CAACqC,IAAI,EAAAJ,QAAA,eACN3B,IAAA,CAACN,IAAI,CAACsC,KAAK,EAAAL,QAAA,CAAEvB,CAAC,CAAC,YAAY,CAAC,CAAa,CAAC,cAC1CJ,IAAA,OAAA2B,QAAA,CAAKtB,YAAY,CAAC4B,UAAU,EAAI,KAAK,CAAK,CAAC,EACpC,CAAC,CACV,CAAC,CACN,CAAC,cACNjC,IAAA,CAACP,GAAG,EAACqC,EAAE,CAAE,CAAE,CAAAH,QAAA,cACP3B,IAAA,CAACN,IAAI,EAACkC,SAAS,CAAC,4BAA4B,CAAAD,QAAA,cACxCzB,KAAA,CAACR,IAAI,CAACqC,IAAI,EAAAJ,QAAA,eACN3B,IAAA,CAACN,IAAI,CAACsC,KAAK,EAAAL,QAAA,CAAEvB,CAAC,CAAC,iBAAiB,CAAC,CAAa,CAAC,cAC/CJ,IAAA,OAAA2B,QAAA,CAAKtB,YAAY,CAAC6B,cAAc,CAAG,GAAG7B,YAAY,CAAC6B,cAAc,CAAG,GAAG,GAAG,CAAG,KAAK,CAAK,CAAC,EACjF,CAAC,CACV,CAAC,CACN,CAAC,cACNlC,IAAA,CAACP,GAAG,EAACqC,EAAE,CAAE,CAAE,CAAAH,QAAA,cACP3B,IAAA,CAACN,IAAI,EAACkC,SAAS,CAAC,yBAAyB,CAAAD,QAAA,cACrCzB,KAAA,CAACR,IAAI,CAACqC,IAAI,EAAAJ,QAAA,eACN3B,IAAA,CAACN,IAAI,CAACsC,KAAK,EAAAL,QAAA,CAAEvB,CAAC,CAAC,YAAY,CAAC,CAAa,CAAC,cAC1CJ,IAAA,OAAA2B,QAAA,CAAKtB,YAAY,CAAC8B,UAAU,EAAI,KAAK,CAAK,CAAC,EACpC,CAAC,CACV,CAAC,CACN,CAAC,EACL,CAAC,cAENjC,KAAA,CAACV,GAAG,EAACoC,SAAS,CAAC,MAAM,CAAAD,QAAA,eACjB3B,IAAA,CAACP,GAAG,EAACqC,EAAE,CAAE,CAAE,CAACF,SAAS,CAAC,aAAa,CAAAD,QAAA,cAC/B3B,IAAA,CAACN,IAAI,EAAAiC,QAAA,cACDzB,KAAA,CAACR,IAAI,CAACqC,IAAI,EAAAJ,QAAA,eACN3B,IAAA,OAAA2B,QAAA,CAAKvB,CAAC,CAAC,mBAAmB,CAAC,CAAK,CAAC,cACjCJ,IAAA,MAAA2B,QAAA,CAAIvB,CAAC,CAAC,wBAAwB,CAAC,CAAI,CAAC,cACpCJ,IAAA,CAACL,MAAM,EAACyC,EAAE,CAAEvC,IAAK,CAACwC,EAAE,CAAC,gBAAgB,CAACC,OAAO,CAAC,SAAS,CAAAX,QAAA,CAAEvB,CAAC,CAAC,mBAAmB,CAAC,CAAS,CAAC,EAClF,CAAC,CACV,CAAC,CACN,CAAC,cACNJ,IAAA,CAACP,GAAG,EAACqC,EAAE,CAAE,CAAE,CAACF,SAAS,CAAC,aAAa,CAAAD,QAAA,cAC/B3B,IAAA,CAACN,IAAI,EAAAiC,QAAA,cACDzB,KAAA,CAACR,IAAI,CAACqC,IAAI,EAAAJ,QAAA,eACN3B,IAAA,OAAA2B,QAAA,CAAKvB,CAAC,CAAC,oBAAoB,CAAC,CAAK,CAAC,cAClCJ,IAAA,MAAA2B,QAAA,CAAIvB,CAAC,CAAC,kBAAkB,CAAC,CAAI,CAAC,cAC9BJ,IAAA,CAACL,MAAM,EAACyC,EAAE,CAAEvC,IAAK,CAACwC,EAAE,CAAC,iBAAiB,CAACC,OAAO,CAAC,SAAS,CAAAX,QAAA,CAAEvB,CAAC,CAAC,uBAAuB,CAAC,CAAS,CAAC,EACvF,CAAC,CACV,CAAC,CACN,CAAC,EACL,CAAC,EACC,CAAC,CAEpB,CAAC,CAED,cAAe,CAAAD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
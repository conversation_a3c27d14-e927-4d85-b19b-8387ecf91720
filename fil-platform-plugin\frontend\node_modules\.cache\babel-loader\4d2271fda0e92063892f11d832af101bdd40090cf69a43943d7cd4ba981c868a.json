{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Container,Row,Col,Card,Table,Badge}from'react-bootstrap';import{getSupabase}from'../../supabaseClient';import{useTranslation}from'react-i18next';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const CustomerAssets=()=>{const{t}=useTranslation();const[assets,setAssets]=useState([]);const[loading,setLoading]=useState(true);useEffect(()=>{const fetchAssets=async()=>{const supabase=getSupabase();if(!supabase)return;setLoading(true);const{data:{user}}=await supabase.auth.getUser();if(!user){setLoading(false);return;// User not logged in\n}// Fetch user assets with user information\nconst{data,error}=await supabase.from('user_assets').select(`\n                    user_id,\n                    currency_code,\n                    balance_available,\n                    balance_locked,\n                    balance_total,\n                    withdrawn_total,\n                    users (\n                        email,\n                        phone,\n                        role\n                    ),\n                    currencies (\n                        code,\n                        total_supply,\n                        withdrawable\n                    )\n                `).order('balance_total',{ascending:false});if(error){console.error('Error fetching customer assets:',error);}else{setAssets(data);}setLoading(false);};fetchAssets();},[]);if(loading){return/*#__PURE__*/_jsx(\"div\",{children:t('loading_customer_assets')});}return/*#__PURE__*/_jsxs(Container,{children:[/*#__PURE__*/_jsx(\"h2\",{className:\"mb-4\",children:t('customer_assets')}),/*#__PURE__*/_jsx(Row,{children:/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Card.Body,{children:/*#__PURE__*/_jsxs(Table,{striped:true,bordered:true,hover:true,responsive:true,children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{children:t('user_email')}),/*#__PURE__*/_jsx(\"th\",{children:t('user_phone')}),/*#__PURE__*/_jsx(\"th\",{children:t('user_role')}),/*#__PURE__*/_jsx(\"th\",{children:t('currency_code')}),/*#__PURE__*/_jsx(\"th\",{children:t('available_balance')}),/*#__PURE__*/_jsx(\"th\",{children:t('locked_balance')}),/*#__PURE__*/_jsx(\"th\",{children:t('total_balance')}),/*#__PURE__*/_jsx(\"th\",{children:t('withdrawn_total')})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:assets.length===0?/*#__PURE__*/_jsx(\"tr\",{children:/*#__PURE__*/_jsx(\"td\",{colSpan:\"8\",className:\"text-center\",children:t('no_customer_assets_available')})}):assets.map((asset,index)=>{var _asset$users,_asset$users2,_asset$users3,_asset$users4,_asset$balance_availa,_asset$balance_locked,_asset$balance_total,_asset$withdrawn_tota;return/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{children:((_asset$users=asset.users)===null||_asset$users===void 0?void 0:_asset$users.email)||'-'}),/*#__PURE__*/_jsx(\"td\",{children:((_asset$users2=asset.users)===null||_asset$users2===void 0?void 0:_asset$users2.phone)||'-'}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(Badge,{bg:((_asset$users3=asset.users)===null||_asset$users3===void 0?void 0:_asset$users3.role)==='customer'?'primary':'secondary',children:((_asset$users4=asset.users)===null||_asset$users4===void 0?void 0:_asset$users4.role)||'-'})}),/*#__PURE__*/_jsx(\"td\",{children:asset.currency_code}),/*#__PURE__*/_jsx(\"td\",{children:((_asset$balance_availa=asset.balance_available)===null||_asset$balance_availa===void 0?void 0:_asset$balance_availa.toFixed(6))||'0.000000'}),/*#__PURE__*/_jsx(\"td\",{children:((_asset$balance_locked=asset.balance_locked)===null||_asset$balance_locked===void 0?void 0:_asset$balance_locked.toFixed(6))||'0.000000'}),/*#__PURE__*/_jsx(\"td\",{children:((_asset$balance_total=asset.balance_total)===null||_asset$balance_total===void 0?void 0:_asset$balance_total.toFixed(6))||'0.000000'}),/*#__PURE__*/_jsx(\"td\",{children:((_asset$withdrawn_tota=asset.withdrawn_total)===null||_asset$withdrawn_tota===void 0?void 0:_asset$withdrawn_tota.toFixed(6))||'0.000000'})]},`${asset.user_id}-${asset.currency_code}`);})})]})})})})})]});};export default CustomerAssets;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "Card", "Table", "Badge", "getSupabase", "useTranslation", "jsx", "_jsx", "jsxs", "_jsxs", "CustomerAssets", "t", "assets", "setAssets", "loading", "setLoading", "fetchAssets", "supabase", "data", "user", "auth", "getUser", "error", "from", "select", "order", "ascending", "console", "children", "className", "Body", "striped", "bordered", "hover", "responsive", "length", "colSpan", "map", "asset", "index", "_asset$users", "_asset$users2", "_asset$users3", "_asset$users4", "_asset$balance_availa", "_asset$balance_locked", "_asset$balance_total", "_asset$withdrawn_tota", "users", "email", "phone", "bg", "role", "currency_code", "balance_available", "toFixed", "balance_locked", "balance_total", "withdrawn_total", "user_id"], "sources": ["D:/New_System/fil-platform-plugin/frontend/src/pages/maker/CustomerAssets.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Table, Badge } from 'react-bootstrap';\nimport { getSupabase } from '../../supabaseClient';\nimport { useTranslation } from 'react-i18next';\n\nconst CustomerAssets = () => {\n    const { t } = useTranslation();\n    const [assets, setAssets] = useState([]);\n    const [loading, setLoading] = useState(true);\n\n    useEffect(() => {\n        const fetchAssets = async () => {\n            const supabase = getSupabase();\n            if (!supabase) return;\n\n            setLoading(true);\n            const { data: { user } } = await supabase.auth.getUser();\n\n            if (!user) {\n                setLoading(false);\n                return; // User not logged in\n            }\n\n            // Fetch user assets with user information\n            const { data, error } = await supabase\n                .from('user_assets')\n                .select(`\n                    user_id,\n                    currency_code,\n                    balance_available,\n                    balance_locked,\n                    balance_total,\n                    withdrawn_total,\n                    users (\n                        email,\n                        phone,\n                        role\n                    ),\n                    currencies (\n                        code,\n                        total_supply,\n                        withdrawable\n                    )\n                `)\n                .order('balance_total', { ascending: false });\n\n            if (error) {\n                console.error('Error fetching customer assets:', error);\n            } else {\n                setAssets(data);\n            }\n            setLoading(false);\n        };\n\n        fetchAssets();\n    }, []);\n\n    if (loading) {\n        return <div>{t('loading_customer_assets')}</div>;\n    }\n\n    return (\n        <Container>\n            <h2 className=\"mb-4\">{t('customer_assets')}</h2>\n            <Row>\n                <Col>\n                    <Card>\n                        <Card.Body>\n                            <Table striped bordered hover responsive>\n                                <thead>\n                                    <tr>\n                                        <th>{t('user_email')}</th>\n                                        <th>{t('user_phone')}</th>\n                                        <th>{t('user_role')}</th>\n                                        <th>{t('currency_code')}</th>\n                                        <th>{t('available_balance')}</th>\n                                        <th>{t('locked_balance')}</th>\n                                        <th>{t('total_balance')}</th>\n                                        <th>{t('withdrawn_total')}</th>\n                                    </tr>\n                                </thead>\n                                <tbody>\n                                    {assets.length === 0 ? (\n                                        <tr>\n                                            <td colSpan=\"8\" className=\"text-center\">{t('no_customer_assets_available')}</td>\n                                        </tr>\n                                    ) : (\n                                        assets.map((asset, index) => (\n                                            <tr key={`${asset.user_id}-${asset.currency_code}`}>\n                                                <td>{asset.users?.email || '-'}</td>\n                                                <td>{asset.users?.phone || '-'}</td>\n                                                <td>\n                                                    <Badge bg={asset.users?.role === 'customer' ? 'primary' : 'secondary'}>\n                                                        {asset.users?.role || '-'}\n                                                    </Badge>\n                                                </td>\n                                                <td>{asset.currency_code}</td>\n                                                <td>{asset.balance_available?.toFixed(6) || '0.000000'}</td>\n                                                <td>{asset.balance_locked?.toFixed(6) || '0.000000'}</td>\n                                                <td>{asset.balance_total?.toFixed(6) || '0.000000'}</td>\n                                                <td>{asset.withdrawn_total?.toFixed(6) || '0.000000'}</td>\n                                            </tr>\n                                        ))\n                                    )}\n                                </tbody>\n                            </Table>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n        </Container>\n    );\n};\n\nexport default CustomerAssets;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,SAAS,CAAEC,GAAG,CAAEC,GAAG,CAAEC,IAAI,CAAEC,KAAK,CAAEC,KAAK,KAAQ,iBAAiB,CACzE,OAASC,WAAW,KAAQ,sBAAsB,CAClD,OAASC,cAAc,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE/C,KAAM,CAAAC,cAAc,CAAGA,CAAA,GAAM,CACzB,KAAM,CAAEC,CAAE,CAAC,CAAGN,cAAc,CAAC,CAAC,CAC9B,KAAM,CAACO,MAAM,CAAEC,SAAS,CAAC,CAAGjB,QAAQ,CAAC,EAAE,CAAC,CACxC,KAAM,CAACkB,OAAO,CAAEC,UAAU,CAAC,CAAGnB,QAAQ,CAAC,IAAI,CAAC,CAE5CC,SAAS,CAAC,IAAM,CACZ,KAAM,CAAAmB,WAAW,CAAG,KAAAA,CAAA,GAAY,CAC5B,KAAM,CAAAC,QAAQ,CAAGb,WAAW,CAAC,CAAC,CAC9B,GAAI,CAACa,QAAQ,CAAE,OAEfF,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAEG,IAAI,CAAE,CAAEC,IAAK,CAAE,CAAC,CAAG,KAAM,CAAAF,QAAQ,CAACG,IAAI,CAACC,OAAO,CAAC,CAAC,CAExD,GAAI,CAACF,IAAI,CAAE,CACPJ,UAAU,CAAC,KAAK,CAAC,CACjB,OAAQ;AACZ,CAEA;AACA,KAAM,CAAEG,IAAI,CAAEI,KAAM,CAAC,CAAG,KAAM,CAAAL,QAAQ,CACjCM,IAAI,CAAC,aAAa,CAAC,CACnBC,MAAM,CAAC;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,CAAC,CACDC,KAAK,CAAC,eAAe,CAAE,CAAEC,SAAS,CAAE,KAAM,CAAC,CAAC,CAEjD,GAAIJ,KAAK,CAAE,CACPK,OAAO,CAACL,KAAK,CAAC,iCAAiC,CAAEA,KAAK,CAAC,CAC3D,CAAC,IAAM,CACHT,SAAS,CAACK,IAAI,CAAC,CACnB,CACAH,UAAU,CAAC,KAAK,CAAC,CACrB,CAAC,CAEDC,WAAW,CAAC,CAAC,CACjB,CAAC,CAAE,EAAE,CAAC,CAEN,GAAIF,OAAO,CAAE,CACT,mBAAOP,IAAA,QAAAqB,QAAA,CAAMjB,CAAC,CAAC,yBAAyB,CAAC,CAAM,CAAC,CACpD,CAEA,mBACIF,KAAA,CAACX,SAAS,EAAA8B,QAAA,eACNrB,IAAA,OAAIsB,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAEjB,CAAC,CAAC,iBAAiB,CAAC,CAAK,CAAC,cAChDJ,IAAA,CAACR,GAAG,EAAA6B,QAAA,cACArB,IAAA,CAACP,GAAG,EAAA4B,QAAA,cACArB,IAAA,CAACN,IAAI,EAAA2B,QAAA,cACDrB,IAAA,CAACN,IAAI,CAAC6B,IAAI,EAAAF,QAAA,cACNnB,KAAA,CAACP,KAAK,EAAC6B,OAAO,MAACC,QAAQ,MAACC,KAAK,MAACC,UAAU,MAAAN,QAAA,eACpCrB,IAAA,UAAAqB,QAAA,cACInB,KAAA,OAAAmB,QAAA,eACIrB,IAAA,OAAAqB,QAAA,CAAKjB,CAAC,CAAC,YAAY,CAAC,CAAK,CAAC,cAC1BJ,IAAA,OAAAqB,QAAA,CAAKjB,CAAC,CAAC,YAAY,CAAC,CAAK,CAAC,cAC1BJ,IAAA,OAAAqB,QAAA,CAAKjB,CAAC,CAAC,WAAW,CAAC,CAAK,CAAC,cACzBJ,IAAA,OAAAqB,QAAA,CAAKjB,CAAC,CAAC,eAAe,CAAC,CAAK,CAAC,cAC7BJ,IAAA,OAAAqB,QAAA,CAAKjB,CAAC,CAAC,mBAAmB,CAAC,CAAK,CAAC,cACjCJ,IAAA,OAAAqB,QAAA,CAAKjB,CAAC,CAAC,gBAAgB,CAAC,CAAK,CAAC,cAC9BJ,IAAA,OAAAqB,QAAA,CAAKjB,CAAC,CAAC,eAAe,CAAC,CAAK,CAAC,cAC7BJ,IAAA,OAAAqB,QAAA,CAAKjB,CAAC,CAAC,iBAAiB,CAAC,CAAK,CAAC,EAC/B,CAAC,CACF,CAAC,cACRJ,IAAA,UAAAqB,QAAA,CACKhB,MAAM,CAACuB,MAAM,GAAK,CAAC,cAChB5B,IAAA,OAAAqB,QAAA,cACIrB,IAAA,OAAI6B,OAAO,CAAC,GAAG,CAACP,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAEjB,CAAC,CAAC,8BAA8B,CAAC,CAAK,CAAC,CAChF,CAAC,CAELC,MAAM,CAACyB,GAAG,CAAC,CAACC,KAAK,CAAEC,KAAK,QAAAC,YAAA,CAAAC,aAAA,CAAAC,aAAA,CAAAC,aAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,oBAAA,CAAAC,qBAAA,oBACpBtC,KAAA,OAAAmB,QAAA,eACIrB,IAAA,OAAAqB,QAAA,CAAK,EAAAY,YAAA,CAAAF,KAAK,CAACU,KAAK,UAAAR,YAAA,iBAAXA,YAAA,CAAaS,KAAK,GAAI,GAAG,CAAK,CAAC,cACpC1C,IAAA,OAAAqB,QAAA,CAAK,EAAAa,aAAA,CAAAH,KAAK,CAACU,KAAK,UAAAP,aAAA,iBAAXA,aAAA,CAAaS,KAAK,GAAI,GAAG,CAAK,CAAC,cACpC3C,IAAA,OAAAqB,QAAA,cACIrB,IAAA,CAACJ,KAAK,EAACgD,EAAE,CAAE,EAAAT,aAAA,CAAAJ,KAAK,CAACU,KAAK,UAAAN,aAAA,iBAAXA,aAAA,CAAaU,IAAI,IAAK,UAAU,CAAG,SAAS,CAAG,WAAY,CAAAxB,QAAA,CACjE,EAAAe,aAAA,CAAAL,KAAK,CAACU,KAAK,UAAAL,aAAA,iBAAXA,aAAA,CAAaS,IAAI,GAAI,GAAG,CACtB,CAAC,CACR,CAAC,cACL7C,IAAA,OAAAqB,QAAA,CAAKU,KAAK,CAACe,aAAa,CAAK,CAAC,cAC9B9C,IAAA,OAAAqB,QAAA,CAAK,EAAAgB,qBAAA,CAAAN,KAAK,CAACgB,iBAAiB,UAAAV,qBAAA,iBAAvBA,qBAAA,CAAyBW,OAAO,CAAC,CAAC,CAAC,GAAI,UAAU,CAAK,CAAC,cAC5DhD,IAAA,OAAAqB,QAAA,CAAK,EAAAiB,qBAAA,CAAAP,KAAK,CAACkB,cAAc,UAAAX,qBAAA,iBAApBA,qBAAA,CAAsBU,OAAO,CAAC,CAAC,CAAC,GAAI,UAAU,CAAK,CAAC,cACzDhD,IAAA,OAAAqB,QAAA,CAAK,EAAAkB,oBAAA,CAAAR,KAAK,CAACmB,aAAa,UAAAX,oBAAA,iBAAnBA,oBAAA,CAAqBS,OAAO,CAAC,CAAC,CAAC,GAAI,UAAU,CAAK,CAAC,cACxDhD,IAAA,OAAAqB,QAAA,CAAK,EAAAmB,qBAAA,CAAAT,KAAK,CAACoB,eAAe,UAAAX,qBAAA,iBAArBA,qBAAA,CAAuBQ,OAAO,CAAC,CAAC,CAAC,GAAI,UAAU,CAAK,CAAC,GAZrD,GAAGjB,KAAK,CAACqB,OAAO,IAAIrB,KAAK,CAACe,aAAa,EAa5C,CAAC,EACR,CACJ,CACE,CAAC,EACL,CAAC,CACD,CAAC,CACV,CAAC,CACN,CAAC,CACL,CAAC,EACC,CAAC,CAEpB,CAAC,CAED,cAAe,CAAA3C,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
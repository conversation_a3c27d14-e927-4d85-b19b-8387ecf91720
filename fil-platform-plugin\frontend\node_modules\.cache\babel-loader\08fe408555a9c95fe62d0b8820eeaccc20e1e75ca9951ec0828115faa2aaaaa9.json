{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Container,Row,Col,Card,Table,Button,Nav}from'react-bootstrap';import{getSupabase}from'../../supabaseClient';import{useTranslation}from'react-i18next';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const WalletPage=()=>{const{t}=useTranslation();const[assets,setAssets]=useState([]);const[loading,setLoading]=useState(true);const[activeTab,setActiveTab]=useState('overview');// overview, deposit, withdraw, exchange\nuseEffect(()=>{const fetchAssets=async()=>{const supabase=getSupabase();if(!supabase)return;setLoading(true);const{data:{user}}=await supabase.auth.getUser();if(!user){setLoading(false);return;// User not logged in\n}const{data,error}=await supabase.from('user_assets').select('*').eq('user_id',user.id);if(error){console.error('Error fetching assets:',error);}else{setAssets(data);}setLoading(false);};fetchAssets();},[]);if(loading){return/*#__PURE__*/_jsx(\"div\",{children:t('loading_wallet')});}const renderContent=()=>{switch(activeTab){case'overview':return/*#__PURE__*/_jsxs(Table,{striped:true,bordered:true,hover:true,responsive:true,children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{children:t('currency')}),/*#__PURE__*/_jsx(\"th\",{children:t('available_balance')}),/*#__PURE__*/_jsx(\"th\",{children:t('locked_balance')}),/*#__PURE__*/_jsx(\"th\",{children:t('total_balance')}),/*#__PURE__*/_jsx(\"th\",{children:t('withdrawn')})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:assets.length===0?/*#__PURE__*/_jsx(\"tr\",{children:/*#__PURE__*/_jsx(\"td\",{colSpan:\"5\",className:\"text-center\",children:t('no_assets')})}):assets.map(asset=>/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{children:asset.currency_code}),/*#__PURE__*/_jsx(\"td\",{children:asset.available_balance}),/*#__PURE__*/_jsx(\"td\",{children:asset.balance_locked}),/*#__PURE__*/_jsx(\"td\",{children:asset.balance_total}),/*#__PURE__*/_jsx(\"td\",{children:asset.withdrawn_total})]},asset.currency_code))})]});case'deposit':return/*#__PURE__*/_jsx(\"div\",{children:t('deposit_coming_soon')});// Placeholder for deposit UI\ncase'withdraw':return/*#__PURE__*/_jsx(\"div\",{children:t('withdraw_coming_soon')});// Placeholder for withdraw UI\ncase'exchange':return/*#__PURE__*/_jsx(\"div\",{children:t('exchange_coming_soon')});// Placeholder for exchange UI\ndefault:return null;}};return/*#__PURE__*/_jsxs(Container,{children:[/*#__PURE__*/_jsx(\"h2\",{className:\"mb-4\",children:t('my_wallet')}),/*#__PURE__*/_jsx(Row,{children:/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsx(Card.Header,{children:/*#__PURE__*/_jsxs(Nav,{variant:\"tabs\",defaultActiveKey:\"overview\",onSelect:selectedKey=>setActiveTab(selectedKey),children:[/*#__PURE__*/_jsx(Nav.Item,{children:/*#__PURE__*/_jsx(Nav.Link,{eventKey:\"overview\",children:t('overview')})}),/*#__PURE__*/_jsx(Nav.Item,{children:/*#__PURE__*/_jsx(Nav.Link,{eventKey:\"deposit\",children:t('deposit')})}),/*#__PURE__*/_jsx(Nav.Item,{children:/*#__PURE__*/_jsx(Nav.Link,{eventKey:\"withdraw\",children:t('withdraw')})}),/*#__PURE__*/_jsx(Nav.Item,{children:/*#__PURE__*/_jsx(Nav.Link,{eventKey:\"exchange\",children:t('exchange')})})]})}),/*#__PURE__*/_jsx(Card.Body,{children:renderContent()})]})})})]});};export default WalletPage;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "Card", "Table", "<PERSON><PERSON>", "Nav", "getSupabase", "useTranslation", "jsx", "_jsx", "jsxs", "_jsxs", "WalletPage", "t", "assets", "setAssets", "loading", "setLoading", "activeTab", "setActiveTab", "fetchAssets", "supabase", "data", "user", "auth", "getUser", "error", "from", "select", "eq", "id", "console", "children", "renderContent", "striped", "bordered", "hover", "responsive", "length", "colSpan", "className", "map", "asset", "currency_code", "available_balance", "balance_locked", "balance_total", "withdrawn_total", "Header", "variant", "defaultActiveKey", "onSelect", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Link", "eventKey", "Body"], "sources": ["D:/New_System/fil-platform-plugin/frontend/src/pages/customer/WalletPage.js"], "sourcesContent": ["\nimport React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Table, Button, Nav } from 'react-bootstrap';\nimport { getSupabase } from '../../supabaseClient';\nimport { useTranslation } from 'react-i18next';\n\nconst WalletPage = () => {\n    const { t } = useTranslation();\n    const [assets, setAssets] = useState([]);\n    const [loading, setLoading] = useState(true);\n    const [activeTab, setActiveTab] = useState('overview'); // overview, deposit, withdraw, exchange\n\n    useEffect(() => {\n        const fetchAssets = async () => {\n            const supabase = getSupabase();\n            if (!supabase) return;\n\n            setLoading(true);\n            const { data: { user } } = await supabase.auth.getUser();\n\n            if (!user) {\n                setLoading(false);\n                return; // User not logged in\n            }\n\n            const { data, error } = await supabase\n                .from('user_assets')\n                .select('*')\n                .eq('user_id', user.id);\n\n            if (error) {\n                console.error('Error fetching assets:', error);\n            } else {\n                setAssets(data);\n            }\n            setLoading(false);\n        };\n\n        fetchAssets();\n    }, []);\n\n    if (loading) {\n        return <div>{t('loading_wallet')}</div>;\n    }\n\n    const renderContent = () => {\n        switch (activeTab) {\n            case 'overview':\n                return (\n                    <Table striped bordered hover responsive>\n                        <thead>\n                            <tr>\n                                <th>{t('currency')}</th>\n                                <th>{t('available_balance')}</th>\n                                <th>{t('locked_balance')}</th>\n                                <th>{t('total_balance')}</th>\n                                <th>{t('withdrawn')}</th>\n                            </tr>\n                        </thead>\n                        <tbody>\n                            {assets.length === 0 ? (\n                                <tr>\n                                    <td colSpan=\"5\" className=\"text-center\">{t('no_assets')}</td>\n                                </tr>\n                            ) : (\n                                assets.map(asset => (\n                                    <tr key={asset.currency_code}>\n                                        <td>{asset.currency_code}</td>\n                                        <td>{asset.available_balance}</td>\n                                        <td>{asset.balance_locked}</td>\n                                        <td>{asset.balance_total}</td>\n                                        <td>{asset.withdrawn_total}</td>\n                                    </tr>\n                                ))\n                            )}\n                        </tbody>\n                    </Table>\n                );\n            case 'deposit':\n                return <div>{t('deposit_coming_soon')}</div>; // Placeholder for deposit UI\n            case 'withdraw':\n                return <div>{t('withdraw_coming_soon')}</div>; // Placeholder for withdraw UI\n            case 'exchange':\n                return <div>{t('exchange_coming_soon')}</div>; // Placeholder for exchange UI\n            default:\n                return null;\n        }\n    };\n\n    return (\n        <Container>\n            <h2 className=\"mb-4\">{t('my_wallet')}</h2>\n            <Row>\n                <Col>\n                    <Card>\n                        <Card.Header>\n                            <Nav variant=\"tabs\" defaultActiveKey=\"overview\" onSelect={(selectedKey) => setActiveTab(selectedKey)}>\n                                <Nav.Item>\n                                    <Nav.Link eventKey=\"overview\">{t('overview')}</Nav.Link>\n                                </Nav.Item>\n                                <Nav.Item>\n                                    <Nav.Link eventKey=\"deposit\">{t('deposit')}</Nav.Link>\n                                </Nav.Item>\n                                <Nav.Item>\n                                    <Nav.Link eventKey=\"withdraw\">{t('withdraw')}</Nav.Link>\n                                </Nav.Item>\n                                <Nav.Item>\n                                    <Nav.Link eventKey=\"exchange\">{t('exchange')}</Nav.Link>\n                                </Nav.Item>\n                            </Nav>\n                        </Card.Header>\n                        <Card.Body>\n                            {renderContent()}\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n        </Container>\n    );\n};\n\nexport default WalletPage;\n"], "mappings": "AACA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,SAAS,CAAEC,GAAG,CAAEC,GAAG,CAAEC,IAAI,CAAEC,KAAK,CAAEC,MAAM,CAAEC,GAAG,KAAQ,iBAAiB,CAC/E,OAASC,WAAW,KAAQ,sBAAsB,CAClD,OAASC,cAAc,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE/C,KAAM,CAAAC,UAAU,CAAGA,CAAA,GAAM,CACrB,KAAM,CAAEC,CAAE,CAAC,CAAGN,cAAc,CAAC,CAAC,CAC9B,KAAM,CAACO,MAAM,CAAEC,SAAS,CAAC,CAAGlB,QAAQ,CAAC,EAAE,CAAC,CACxC,KAAM,CAACmB,OAAO,CAAEC,UAAU,CAAC,CAAGpB,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACqB,SAAS,CAAEC,YAAY,CAAC,CAAGtB,QAAQ,CAAC,UAAU,CAAC,CAAE;AAExDC,SAAS,CAAC,IAAM,CACZ,KAAM,CAAAsB,WAAW,CAAG,KAAAA,CAAA,GAAY,CAC5B,KAAM,CAAAC,QAAQ,CAAGf,WAAW,CAAC,CAAC,CAC9B,GAAI,CAACe,QAAQ,CAAE,OAEfJ,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAEK,IAAI,CAAE,CAAEC,IAAK,CAAE,CAAC,CAAG,KAAM,CAAAF,QAAQ,CAACG,IAAI,CAACC,OAAO,CAAC,CAAC,CAExD,GAAI,CAACF,IAAI,CAAE,CACPN,UAAU,CAAC,KAAK,CAAC,CACjB,OAAQ;AACZ,CAEA,KAAM,CAAEK,IAAI,CAAEI,KAAM,CAAC,CAAG,KAAM,CAAAL,QAAQ,CACjCM,IAAI,CAAC,aAAa,CAAC,CACnBC,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,SAAS,CAAEN,IAAI,CAACO,EAAE,CAAC,CAE3B,GAAIJ,KAAK,CAAE,CACPK,OAAO,CAACL,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAClD,CAAC,IAAM,CACHX,SAAS,CAACO,IAAI,CAAC,CACnB,CACAL,UAAU,CAAC,KAAK,CAAC,CACrB,CAAC,CAEDG,WAAW,CAAC,CAAC,CACjB,CAAC,CAAE,EAAE,CAAC,CAEN,GAAIJ,OAAO,CAAE,CACT,mBAAOP,IAAA,QAAAuB,QAAA,CAAMnB,CAAC,CAAC,gBAAgB,CAAC,CAAM,CAAC,CAC3C,CAEA,KAAM,CAAAoB,aAAa,CAAGA,CAAA,GAAM,CACxB,OAAQf,SAAS,EACb,IAAK,UAAU,CACX,mBACIP,KAAA,CAACR,KAAK,EAAC+B,OAAO,MAACC,QAAQ,MAACC,KAAK,MAACC,UAAU,MAAAL,QAAA,eACpCvB,IAAA,UAAAuB,QAAA,cACIrB,KAAA,OAAAqB,QAAA,eACIvB,IAAA,OAAAuB,QAAA,CAAKnB,CAAC,CAAC,UAAU,CAAC,CAAK,CAAC,cACxBJ,IAAA,OAAAuB,QAAA,CAAKnB,CAAC,CAAC,mBAAmB,CAAC,CAAK,CAAC,cACjCJ,IAAA,OAAAuB,QAAA,CAAKnB,CAAC,CAAC,gBAAgB,CAAC,CAAK,CAAC,cAC9BJ,IAAA,OAAAuB,QAAA,CAAKnB,CAAC,CAAC,eAAe,CAAC,CAAK,CAAC,cAC7BJ,IAAA,OAAAuB,QAAA,CAAKnB,CAAC,CAAC,WAAW,CAAC,CAAK,CAAC,EACzB,CAAC,CACF,CAAC,cACRJ,IAAA,UAAAuB,QAAA,CACKlB,MAAM,CAACwB,MAAM,GAAK,CAAC,cAChB7B,IAAA,OAAAuB,QAAA,cACIvB,IAAA,OAAI8B,OAAO,CAAC,GAAG,CAACC,SAAS,CAAC,aAAa,CAAAR,QAAA,CAAEnB,CAAC,CAAC,WAAW,CAAC,CAAK,CAAC,CAC7D,CAAC,CAELC,MAAM,CAAC2B,GAAG,CAACC,KAAK,eACZ/B,KAAA,OAAAqB,QAAA,eACIvB,IAAA,OAAAuB,QAAA,CAAKU,KAAK,CAACC,aAAa,CAAK,CAAC,cAC9BlC,IAAA,OAAAuB,QAAA,CAAKU,KAAK,CAACE,iBAAiB,CAAK,CAAC,cAClCnC,IAAA,OAAAuB,QAAA,CAAKU,KAAK,CAACG,cAAc,CAAK,CAAC,cAC/BpC,IAAA,OAAAuB,QAAA,CAAKU,KAAK,CAACI,aAAa,CAAK,CAAC,cAC9BrC,IAAA,OAAAuB,QAAA,CAAKU,KAAK,CAACK,eAAe,CAAK,CAAC,GAL3BL,KAAK,CAACC,aAMX,CACP,CACJ,CACE,CAAC,EACL,CAAC,CAEhB,IAAK,SAAS,CACV,mBAAOlC,IAAA,QAAAuB,QAAA,CAAMnB,CAAC,CAAC,qBAAqB,CAAC,CAAM,CAAC,CAAE;AAClD,IAAK,UAAU,CACX,mBAAOJ,IAAA,QAAAuB,QAAA,CAAMnB,CAAC,CAAC,sBAAsB,CAAC,CAAM,CAAC,CAAE;AACnD,IAAK,UAAU,CACX,mBAAOJ,IAAA,QAAAuB,QAAA,CAAMnB,CAAC,CAAC,sBAAsB,CAAC,CAAM,CAAC,CAAE;AACnD,QACI,MAAO,KAAI,CACnB,CACJ,CAAC,CAED,mBACIF,KAAA,CAACZ,SAAS,EAAAiC,QAAA,eACNvB,IAAA,OAAI+B,SAAS,CAAC,MAAM,CAAAR,QAAA,CAAEnB,CAAC,CAAC,WAAW,CAAC,CAAK,CAAC,cAC1CJ,IAAA,CAACT,GAAG,EAAAgC,QAAA,cACAvB,IAAA,CAACR,GAAG,EAAA+B,QAAA,cACArB,KAAA,CAACT,IAAI,EAAA8B,QAAA,eACDvB,IAAA,CAACP,IAAI,CAAC8C,MAAM,EAAAhB,QAAA,cACRrB,KAAA,CAACN,GAAG,EAAC4C,OAAO,CAAC,MAAM,CAACC,gBAAgB,CAAC,UAAU,CAACC,QAAQ,CAAGC,WAAW,EAAKjC,YAAY,CAACiC,WAAW,CAAE,CAAApB,QAAA,eACjGvB,IAAA,CAACJ,GAAG,CAACgD,IAAI,EAAArB,QAAA,cACLvB,IAAA,CAACJ,GAAG,CAACiD,IAAI,EAACC,QAAQ,CAAC,UAAU,CAAAvB,QAAA,CAAEnB,CAAC,CAAC,UAAU,CAAC,CAAW,CAAC,CAClD,CAAC,cACXJ,IAAA,CAACJ,GAAG,CAACgD,IAAI,EAAArB,QAAA,cACLvB,IAAA,CAACJ,GAAG,CAACiD,IAAI,EAACC,QAAQ,CAAC,SAAS,CAAAvB,QAAA,CAAEnB,CAAC,CAAC,SAAS,CAAC,CAAW,CAAC,CAChD,CAAC,cACXJ,IAAA,CAACJ,GAAG,CAACgD,IAAI,EAAArB,QAAA,cACLvB,IAAA,CAACJ,GAAG,CAACiD,IAAI,EAACC,QAAQ,CAAC,UAAU,CAAAvB,QAAA,CAAEnB,CAAC,CAAC,UAAU,CAAC,CAAW,CAAC,CAClD,CAAC,cACXJ,IAAA,CAACJ,GAAG,CAACgD,IAAI,EAAArB,QAAA,cACLvB,IAAA,CAACJ,GAAG,CAACiD,IAAI,EAACC,QAAQ,CAAC,UAAU,CAAAvB,QAAA,CAAEnB,CAAC,CAAC,UAAU,CAAC,CAAW,CAAC,CAClD,CAAC,EACV,CAAC,CACG,CAAC,cACdJ,IAAA,CAACP,IAAI,CAACsD,IAAI,EAAAxB,QAAA,CACLC,aAAa,CAAC,CAAC,CACT,CAAC,EACV,CAAC,CACN,CAAC,CACL,CAAC,EACC,CAAC,CAEpB,CAAC,CAED,cAAe,CAAArB,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
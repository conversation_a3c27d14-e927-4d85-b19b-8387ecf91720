[{"D:\\New_System\\fil-platform-plugin\\frontend\\src\\index.js": "1", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\App.js": "2", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\i18n.js": "3", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\supabaseClient.js": "4", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\LoginPage.js": "5", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MakerOrderListPage.js": "6", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\Dashboard.js": "7", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\RecommendPage.js": "8", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MakerProductListPage.js": "9", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\KycPage.js": "10", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\MyGainsPage.js": "11", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\MyAccountPage.js": "12", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\WalletPage.js": "13", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\OrderListPage.js": "14", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\ProductListPage.js": "15", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\Dashboard.js": "16", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\Dashboard.js": "17", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\AgentProductListPage.js": "18", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\AgentMemberList.js": "19", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MakerMiners.js": "20", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MakerFacilities.js": "21", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MinerSnapshots.js": "22", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\Transactions.js": "23", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MinerEarnings.js": "24", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\OrderReports.js": "25", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\OrderDistributions.js": "26", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\CustomerAssets.js": "27", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\NetworkStats.js": "28", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\CoinBatches.js": "29", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\ManualDeposits.js": "30", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\CapacityRequest.js": "31", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\DebugAgent.js": "32"}, {"size": 408, "mtime": 1751951658003, "results": "33", "hashOfConfig": "34"}, {"size": 13000, "mtime": 1752122480345, "results": "35", "hashOfConfig": "34"}, {"size": 39329, "mtime": 1752120908238, "results": "36", "hashOfConfig": "34"}, {"size": 1212, "mtime": 1751873051207, "results": "37", "hashOfConfig": "34"}, {"size": 3250, "mtime": 1751953679420, "results": "38", "hashOfConfig": "34"}, {"size": 4791, "mtime": 1751960448046, "results": "39", "hashOfConfig": "34"}, {"size": 3994, "mtime": 1752113564124, "results": "40", "hashOfConfig": "34"}, {"size": 4610, "mtime": 1751946228349, "results": "41", "hashOfConfig": "34"}, {"size": 5273, "mtime": 1751960463052, "results": "42", "hashOfConfig": "34"}, {"size": 8598, "mtime": 1751939997191, "results": "43", "hashOfConfig": "34"}, {"size": 4230, "mtime": 1751940026705, "results": "44", "hashOfConfig": "34"}, {"size": 1735, "mtime": 1751940008151, "results": "45", "hashOfConfig": "34"}, {"size": 4711, "mtime": 1752120569812, "results": "46", "hashOfConfig": "34"}, {"size": 4495, "mtime": 1751940037703, "results": "47", "hashOfConfig": "34"}, {"size": 3655, "mtime": 1751948557098, "results": "48", "hashOfConfig": "34"}, {"size": 4863, "mtime": 1751950041198, "results": "49", "hashOfConfig": "34"}, {"size": 4299, "mtime": 1752122420540, "results": "50", "hashOfConfig": "34"}, {"size": 4027, "mtime": 1751945104895, "results": "51", "hashOfConfig": "34"}, {"size": 3679, "mtime": 1751944188070, "results": "52", "hashOfConfig": "34"}, {"size": 13565, "mtime": 1752120431667, "results": "53", "hashOfConfig": "34"}, {"size": 10902, "mtime": 1752120494989, "results": "54", "hashOfConfig": "34"}, {"size": 4650, "mtime": 1752111918233, "results": "55", "hashOfConfig": "34"}, {"size": 5979, "mtime": 1752111934504, "results": "56", "hashOfConfig": "34"}, {"size": 4355, "mtime": 1752111904775, "results": "57", "hashOfConfig": "34"}, {"size": 6983, "mtime": 1752115841378, "results": "58", "hashOfConfig": "34"}, {"size": 7638, "mtime": 1752115874068, "results": "59", "hashOfConfig": "34"}, {"size": 4798, "mtime": 1752121974201, "results": "60", "hashOfConfig": "34"}, {"size": 3286, "mtime": 1752114312287, "results": "61", "hashOfConfig": "34"}, {"size": 6196, "mtime": 1752114298217, "results": "62", "hashOfConfig": "34"}, {"size": 7110, "mtime": 1752120864192, "results": "63", "hashOfConfig": "34"}, {"size": 7223, "mtime": 1752119937411, "results": "64", "hashOfConfig": "34"}, {"size": 7467, "mtime": 1752122455950, "results": "65", "hashOfConfig": "34"}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ji7irk", {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\New_System\\fil-platform-plugin\\frontend\\src\\index.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\App.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\i18n.js", ["162", "163", "164", "165", "166", "167", "168", "169", "170"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\supabaseClient.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\LoginPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MakerOrderListPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\Dashboard.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\RecommendPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MakerProductListPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\KycPage.js", ["171"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\MyGainsPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\MyAccountPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\WalletPage.js", ["172"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\OrderListPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\ProductListPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\Dashboard.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\Dashboard.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\AgentProductListPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\AgentMemberList.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MakerMiners.js", ["173"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MakerFacilities.js", ["174"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MinerSnapshots.js", ["175"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\Transactions.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MinerEarnings.js", ["176"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\OrderReports.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\OrderDistributions.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\CustomerAssets.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\NetworkStats.js", ["177"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\CoinBatches.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\ManualDeposits.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\CapacityRequest.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\DebugAgent.js", ["178", "179"], [], {"ruleId": "180", "severity": 1, "message": "181", "line": 138, "column": 7, "nodeType": "182", "messageId": "183", "endLine": 138, "endColumn": 18}, {"ruleId": "180", "severity": 1, "message": "184", "line": 140, "column": 7, "nodeType": "182", "messageId": "183", "endLine": 140, "endColumn": 23}, {"ruleId": "180", "severity": 1, "message": "185", "line": 251, "column": 7, "nodeType": "182", "messageId": "183", "endLine": 251, "endColumn": 17}, {"ruleId": "180", "severity": 1, "message": "181", "line": 431, "column": 7, "nodeType": "182", "messageId": "183", "endLine": 431, "endColumn": 18}, {"ruleId": "180", "severity": 1, "message": "184", "line": 433, "column": 7, "nodeType": "182", "messageId": "183", "endLine": 433, "endColumn": 23}, {"ruleId": "180", "severity": 1, "message": "185", "line": 544, "column": 7, "nodeType": "182", "messageId": "183", "endLine": 544, "endColumn": 17}, {"ruleId": "180", "severity": 1, "message": "181", "line": 724, "column": 7, "nodeType": "182", "messageId": "183", "endLine": 724, "endColumn": 18}, {"ruleId": "180", "severity": 1, "message": "184", "line": 726, "column": 7, "nodeType": "182", "messageId": "183", "endLine": 726, "endColumn": 23}, {"ruleId": "180", "severity": 1, "message": "185", "line": 837, "column": 7, "nodeType": "182", "messageId": "183", "endLine": 837, "endColumn": 17}, {"ruleId": "186", "severity": 1, "message": "187", "line": 49, "column": 8, "nodeType": "188", "endLine": 49, "endColumn": 10, "suggestions": "189"}, {"ruleId": "190", "severity": 1, "message": "191", "line": 3, "column": 44, "nodeType": "192", "messageId": "193", "endLine": 3, "endColumn": 50}, {"ruleId": "190", "severity": 1, "message": "194", "line": 2, "column": 44, "nodeType": "192", "messageId": "193", "endLine": 2, "endColumn": 49}, {"ruleId": "190", "severity": 1, "message": "194", "line": 2, "column": 44, "nodeType": "192", "messageId": "193", "endLine": 2, "endColumn": 49}, {"ruleId": "190", "severity": 1, "message": "194", "line": 2, "column": 44, "nodeType": "192", "messageId": "193", "endLine": 2, "endColumn": 49}, {"ruleId": "190", "severity": 1, "message": "194", "line": 2, "column": 44, "nodeType": "192", "messageId": "193", "endLine": 2, "endColumn": 49}, {"ruleId": "190", "severity": 1, "message": "194", "line": 2, "column": 44, "nodeType": "192", "messageId": "193", "endLine": 2, "endColumn": 49}, {"ruleId": "190", "severity": 1, "message": "195", "line": 7, "column": 13, "nodeType": "192", "messageId": "193", "endLine": 7, "endColumn": 14}, {"ruleId": "190", "severity": 1, "message": "196", "line": 90, "column": 21, "nodeType": "192", "messageId": "193", "endLine": 90, "endColumn": 25}, "no-dupe-keys", "Duplicate key 'no_assets'.", "ObjectExpression", "unexpected", "Duplicate key 'my_invite_code'.", "Duplicate key 'batch_id'.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 't'. Either include it or remove the dependency array.", "ArrayExpression", ["197"], "no-unused-vars", "'Button' is defined but never used.", "Identifier", "unusedVar", "'Badge' is defined but never used.", "'t' is assigned a value but never used.", "'data' is assigned a value but never used.", {"desc": "198", "fix": "199"}, "Update the dependencies array to be: [t]", {"range": "200", "text": "201"}, [1977, 1979], "[t]"]
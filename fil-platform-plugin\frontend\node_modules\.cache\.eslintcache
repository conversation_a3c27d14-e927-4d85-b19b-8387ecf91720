[{"D:\\New_System\\fil-platform-plugin\\frontend\\src\\index.js": "1", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\App.js": "2", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\i18n.js": "3", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\supabaseClient.js": "4", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\LoginPage.js": "5", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MakerOrderListPage.js": "6", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\Dashboard.js": "7", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\RecommendPage.js": "8", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MakerProductListPage.js": "9", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\KycPage.js": "10", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\MyGainsPage.js": "11", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\MyAccountPage.js": "12", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\WalletPage.js": "13", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\OrderListPage.js": "14", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\ProductListPage.js": "15", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\Dashboard.js": "16", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\Dashboard.js": "17", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\AgentProductListPage.js": "18", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\AgentMemberList.js": "19", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MakerMiners.js": "20", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MakerFacilities.js": "21", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MinerSnapshots.js": "22", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\Transactions.js": "23", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MinerEarnings.js": "24", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\OrderReports.js": "25", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\OrderDistributions.js": "26", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\CustomerAssets.js": "27", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\NetworkStats.js": "28", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\CoinBatches.js": "29", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\ManualDeposits.js": "30", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\CapacityRequest.js": "31"}, {"size": 408, "mtime": 1751951658003, "results": "32", "hashOfConfig": "33"}, {"size": 12829, "mtime": 1752121208522, "results": "34", "hashOfConfig": "33"}, {"size": 39329, "mtime": 1752120908238, "results": "35", "hashOfConfig": "33"}, {"size": 1212, "mtime": 1751873051207, "results": "36", "hashOfConfig": "33"}, {"size": 3250, "mtime": 1751953679420, "results": "37", "hashOfConfig": "33"}, {"size": 4791, "mtime": 1751960448046, "results": "38", "hashOfConfig": "33"}, {"size": 3994, "mtime": 1752113564124, "results": "39", "hashOfConfig": "33"}, {"size": 4610, "mtime": 1751946228349, "results": "40", "hashOfConfig": "33"}, {"size": 5273, "mtime": 1751960463052, "results": "41", "hashOfConfig": "33"}, {"size": 8598, "mtime": 1751939997191, "results": "42", "hashOfConfig": "33"}, {"size": 4230, "mtime": 1751940026705, "results": "43", "hashOfConfig": "33"}, {"size": 1735, "mtime": 1751940008151, "results": "44", "hashOfConfig": "33"}, {"size": 4711, "mtime": 1752120569812, "results": "45", "hashOfConfig": "33"}, {"size": 4495, "mtime": 1751940037703, "results": "46", "hashOfConfig": "33"}, {"size": 3655, "mtime": 1751948557098, "results": "47", "hashOfConfig": "33"}, {"size": 4863, "mtime": 1751950041198, "results": "48", "hashOfConfig": "33"}, {"size": 3929, "mtime": 1751946465633, "results": "49", "hashOfConfig": "33"}, {"size": 4027, "mtime": 1751945104895, "results": "50", "hashOfConfig": "33"}, {"size": 3679, "mtime": 1751944188070, "results": "51", "hashOfConfig": "33"}, {"size": 13565, "mtime": 1752120431667, "results": "52", "hashOfConfig": "33"}, {"size": 10902, "mtime": 1752120494989, "results": "53", "hashOfConfig": "33"}, {"size": 4650, "mtime": 1752111918233, "results": "54", "hashOfConfig": "33"}, {"size": 5979, "mtime": 1752111934504, "results": "55", "hashOfConfig": "33"}, {"size": 4355, "mtime": 1752111904775, "results": "56", "hashOfConfig": "33"}, {"size": 6983, "mtime": 1752115841378, "results": "57", "hashOfConfig": "33"}, {"size": 7638, "mtime": 1752115874068, "results": "58", "hashOfConfig": "33"}, {"size": 4798, "mtime": 1752120536096, "results": "59", "hashOfConfig": "33"}, {"size": 3286, "mtime": 1752114312287, "results": "60", "hashOfConfig": "33"}, {"size": 6196, "mtime": 1752114298217, "results": "61", "hashOfConfig": "33"}, {"size": 7110, "mtime": 1752120864192, "results": "62", "hashOfConfig": "33"}, {"size": 7223, "mtime": 1752119937411, "results": "63", "hashOfConfig": "33"}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ji7irk", {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\New_System\\fil-platform-plugin\\frontend\\src\\index.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\App.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\i18n.js", ["157", "158", "159", "160", "161", "162", "163", "164", "165"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\supabaseClient.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\LoginPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MakerOrderListPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\Dashboard.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\RecommendPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MakerProductListPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\KycPage.js", ["166"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\MyGainsPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\MyAccountPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\WalletPage.js", ["167"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\OrderListPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\ProductListPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\Dashboard.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\Dashboard.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\AgentProductListPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\AgentMemberList.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MakerMiners.js", ["168"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MakerFacilities.js", ["169"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MinerSnapshots.js", ["170"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\Transactions.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MinerEarnings.js", ["171"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\OrderReports.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\OrderDistributions.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\CustomerAssets.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\NetworkStats.js", ["172"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\CoinBatches.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\ManualDeposits.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\CapacityRequest.js", [], [], {"ruleId": "173", "severity": 1, "message": "174", "line": 138, "column": 7, "nodeType": "175", "messageId": "176", "endLine": 138, "endColumn": 18}, {"ruleId": "173", "severity": 1, "message": "177", "line": 140, "column": 7, "nodeType": "175", "messageId": "176", "endLine": 140, "endColumn": 23}, {"ruleId": "173", "severity": 1, "message": "178", "line": 251, "column": 7, "nodeType": "175", "messageId": "176", "endLine": 251, "endColumn": 17}, {"ruleId": "173", "severity": 1, "message": "174", "line": 431, "column": 7, "nodeType": "175", "messageId": "176", "endLine": 431, "endColumn": 18}, {"ruleId": "173", "severity": 1, "message": "177", "line": 433, "column": 7, "nodeType": "175", "messageId": "176", "endLine": 433, "endColumn": 23}, {"ruleId": "173", "severity": 1, "message": "178", "line": 544, "column": 7, "nodeType": "175", "messageId": "176", "endLine": 544, "endColumn": 17}, {"ruleId": "173", "severity": 1, "message": "174", "line": 724, "column": 7, "nodeType": "175", "messageId": "176", "endLine": 724, "endColumn": 18}, {"ruleId": "173", "severity": 1, "message": "177", "line": 726, "column": 7, "nodeType": "175", "messageId": "176", "endLine": 726, "endColumn": 23}, {"ruleId": "173", "severity": 1, "message": "178", "line": 837, "column": 7, "nodeType": "175", "messageId": "176", "endLine": 837, "endColumn": 17}, {"ruleId": "179", "severity": 1, "message": "180", "line": 49, "column": 8, "nodeType": "181", "endLine": 49, "endColumn": 10, "suggestions": "182"}, {"ruleId": "183", "severity": 1, "message": "184", "line": 3, "column": 44, "nodeType": "185", "messageId": "186", "endLine": 3, "endColumn": 50}, {"ruleId": "183", "severity": 1, "message": "187", "line": 2, "column": 44, "nodeType": "185", "messageId": "186", "endLine": 2, "endColumn": 49}, {"ruleId": "183", "severity": 1, "message": "187", "line": 2, "column": 44, "nodeType": "185", "messageId": "186", "endLine": 2, "endColumn": 49}, {"ruleId": "183", "severity": 1, "message": "187", "line": 2, "column": 44, "nodeType": "185", "messageId": "186", "endLine": 2, "endColumn": 49}, {"ruleId": "183", "severity": 1, "message": "187", "line": 2, "column": 44, "nodeType": "185", "messageId": "186", "endLine": 2, "endColumn": 49}, {"ruleId": "183", "severity": 1, "message": "187", "line": 2, "column": 44, "nodeType": "185", "messageId": "186", "endLine": 2, "endColumn": 49}, "no-dupe-keys", "Duplicate key 'no_assets'.", "ObjectExpression", "unexpected", "Duplicate key 'my_invite_code'.", "Duplicate key 'batch_id'.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 't'. Either include it or remove the dependency array.", "ArrayExpression", ["188"], "no-unused-vars", "'Button' is defined but never used.", "Identifier", "unusedVar", "'Badge' is defined but never used.", {"desc": "189", "fix": "190"}, "Update the dependencies array to be: [t]", {"range": "191", "text": "192"}, [1977, 1979], "[t]"]
{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Con<PERSON>er,<PERSON>,Col,Card,Button,Alert}from'react-bootstrap';import{useTranslation}from'react-i18next';import{getSupabase}from'../../supabaseClient';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const DebugAgent=()=>{const{t}=useTranslation();const[debugInfo,setDebugInfo]=useState(null);const[loading,setLoading]=useState(true);useEffect(()=>{const fetchDebugData=async()=>{const supabase=getSupabase();if(!supabase){setDebugInfo({error:'Supabase not initialized'});setLoading(false);return;}setLoading(true);const{data:{user}}=await supabase.auth.getUser();if(!user){setDebugInfo({error:'User not logged in'});setLoading(false);return;}const info={userId:user.id,userEmail:user.email,roleFromLocalStorage:localStorage.getItem('user_role'),userMetadata:user.user_metadata};// Check users table\ntry{const{data:userData,error:userError}=await supabase.from('users').select('*').eq('id',user.id).single();info.usersTableData=userData;info.usersTableError=userError;}catch(err){info.usersTableError=err;}// Check agent_profiles table\ntry{const{data:agentData,error:agentError}=await supabase.from('agent_profiles').select('*').eq('user_id',user.id).single();info.agentProfileData=agentData;info.agentProfileError=agentError;}catch(err){info.agentProfileError=err;}// Check maker_profiles table (in case user is also a maker)\ntry{const{data:makerData,error:makerError}=await supabase.from('maker_profiles').select('*').eq('user_id',user.id).single();info.makerProfileData=makerData;info.makerProfileError=makerError;}catch(err){info.makerProfileError=err;}setDebugInfo(info);setLoading(false);};fetchDebugData();},[]);const createAgentProfile=async()=>{const supabase=getSupabase();if(!supabase||!debugInfo)return;try{const{data,error}=await supabase.from('agent_profiles').insert([{user_id:debugInfo.userId,brand_name:'Default Agent',commission_pct:0.05,kyc_status:'pending'}]).select().single();if(error){alert('Error creating agent profile: '+error.message);}else{alert('Agent profile created successfully!');window.location.reload();}}catch(err){alert('Error: '+err.message);}};if(loading){return/*#__PURE__*/_jsx(\"div\",{children:\"Loading debug information...\"});}return/*#__PURE__*/_jsxs(Container,{fluid:true,children:[/*#__PURE__*/_jsx(Row,{className:\"mb-3\",children:/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(\"h2\",{children:\"Agent Debug Information\"})})}),debugInfo&&/*#__PURE__*/_jsx(Row,{children:/*#__PURE__*/_jsxs(Col,{children:[/*#__PURE__*/_jsxs(Card,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Card.Header,{children:\"User Information\"}),/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"User ID:\"}),\" \",debugInfo.userId]}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Email:\"}),\" \",debugInfo.userEmail]}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Role from localStorage:\"}),\" \",debugInfo.roleFromLocalStorage]}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"User Metadata:\"}),\" \",JSON.stringify(debugInfo.userMetadata,null,2)]})]})]}),/*#__PURE__*/_jsxs(Card,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Card.Header,{children:\"Users Table Data\"}),/*#__PURE__*/_jsx(Card.Body,{children:debugInfo.usersTableError?/*#__PURE__*/_jsxs(Alert,{variant:\"danger\",children:[\"Error: \",JSON.stringify(debugInfo.usersTableError,null,2)]}):/*#__PURE__*/_jsx(\"pre\",{children:JSON.stringify(debugInfo.usersTableData,null,2)})})]}),/*#__PURE__*/_jsxs(Card,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Card.Header,{children:\"Agent Profile Data\"}),/*#__PURE__*/_jsx(Card.Body,{children:debugInfo.agentProfileError?/*#__PURE__*/_jsxs(Alert,{variant:\"danger\",children:[\"Error: \",JSON.stringify(debugInfo.agentProfileError,null,2),debugInfo.agentProfileError.code==='PGRST116'&&/*#__PURE__*/_jsxs(\"div\",{className:\"mt-2\",children:[/*#__PURE__*/_jsx(\"p\",{children:\"No agent profile found. This is likely the cause of the issue.\"}),/*#__PURE__*/_jsx(Button,{onClick:createAgentProfile,variant:\"primary\",children:\"Create Agent Profile\"})]})]}):/*#__PURE__*/_jsx(\"pre\",{children:JSON.stringify(debugInfo.agentProfileData,null,2)})})]}),/*#__PURE__*/_jsxs(Card,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Card.Header,{children:\"Maker Profile Data (for reference)\"}),/*#__PURE__*/_jsx(Card.Body,{children:debugInfo.makerProfileError?/*#__PURE__*/_jsxs(Alert,{variant:\"warning\",children:[\"Error: \",JSON.stringify(debugInfo.makerProfileError,null,2)]}):/*#__PURE__*/_jsx(\"pre\",{children:JSON.stringify(debugInfo.makerProfileData,null,2)})})]})]})})]});};export default DebugAgent;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "Card", "<PERSON><PERSON>", "<PERSON><PERSON>", "useTranslation", "getSupabase", "jsx", "_jsx", "jsxs", "_jsxs", "DebugAgent", "t", "debugInfo", "setDebugInfo", "loading", "setLoading", "fetchDebugData", "supabase", "error", "data", "user", "auth", "getUser", "info", "userId", "id", "userEmail", "email", "roleFromLocalStorage", "localStorage", "getItem", "userMetadata", "user_metadata", "userData", "userError", "from", "select", "eq", "single", "usersTableData", "usersTableError", "err", "agentData", "agent<PERSON><PERSON>r", "agentProfileData", "agentProfileError", "makerData", "makerError", "makerProfileData", "makerProfileError", "createAgentProfile", "insert", "user_id", "brand_name", "commission_pct", "kyc_status", "alert", "message", "window", "location", "reload", "children", "fluid", "className", "Header", "Body", "JSON", "stringify", "variant", "code", "onClick"], "sources": ["D:/New_System/fil-platform-plugin/frontend/src/pages/agent/DebugAgent.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Contain<PERSON>, <PERSON>, Col, Card, Button, Alert } from 'react-bootstrap';\nimport { useTranslation } from 'react-i18next';\nimport { getSupabase } from '../../supabaseClient';\n\nconst DebugAgent = () => {\n    const { t } = useTranslation();\n    const [debugInfo, setDebugInfo] = useState(null);\n    const [loading, setLoading] = useState(true);\n\n    useEffect(() => {\n        const fetchDebugData = async () => {\n            const supabase = getSupabase();\n            if (!supabase) {\n                setDebugInfo({ error: 'Supabase not initialized' });\n                setLoading(false);\n                return;\n            }\n\n            setLoading(true);\n            const { data: { user } } = await supabase.auth.getUser();\n\n            if (!user) {\n                setDebugInfo({ error: 'User not logged in' });\n                setLoading(false);\n                return;\n            }\n\n            const info = {\n                userId: user.id,\n                userEmail: user.email,\n                roleFromLocalStorage: localStorage.getItem('user_role'),\n                userMetadata: user.user_metadata,\n            };\n\n            // Check users table\n            try {\n                const { data: userData, error: userError } = await supabase\n                    .from('users')\n                    .select('*')\n                    .eq('id', user.id)\n                    .single();\n                \n                info.usersTableData = userData;\n                info.usersTableError = userError;\n            } catch (err) {\n                info.usersTableError = err;\n            }\n\n            // Check agent_profiles table\n            try {\n                const { data: agentData, error: agentError } = await supabase\n                    .from('agent_profiles')\n                    .select('*')\n                    .eq('user_id', user.id)\n                    .single();\n                \n                info.agentProfileData = agentData;\n                info.agentProfileError = agentError;\n            } catch (err) {\n                info.agentProfileError = err;\n            }\n\n            // Check maker_profiles table (in case user is also a maker)\n            try {\n                const { data: makerData, error: makerError } = await supabase\n                    .from('maker_profiles')\n                    .select('*')\n                    .eq('user_id', user.id)\n                    .single();\n                \n                info.makerProfileData = makerData;\n                info.makerProfileError = makerError;\n            } catch (err) {\n                info.makerProfileError = err;\n            }\n\n            setDebugInfo(info);\n            setLoading(false);\n        };\n\n        fetchDebugData();\n    }, []);\n\n    const createAgentProfile = async () => {\n        const supabase = getSupabase();\n        if (!supabase || !debugInfo) return;\n\n        try {\n            const { data, error } = await supabase\n                .from('agent_profiles')\n                .insert([\n                    {\n                        user_id: debugInfo.userId,\n                        brand_name: 'Default Agent',\n                        commission_pct: 0.05,\n                        kyc_status: 'pending'\n                    }\n                ])\n                .select()\n                .single();\n\n            if (error) {\n                alert('Error creating agent profile: ' + error.message);\n            } else {\n                alert('Agent profile created successfully!');\n                window.location.reload();\n            }\n        } catch (err) {\n            alert('Error: ' + err.message);\n        }\n    };\n\n    if (loading) {\n        return <div>Loading debug information...</div>;\n    }\n\n    return (\n        <Container fluid>\n            <Row className=\"mb-3\">\n                <Col>\n                    <h2>Agent Debug Information</h2>\n                </Col>\n            </Row>\n\n            {debugInfo && (\n                <Row>\n                    <Col>\n                        <Card className=\"mb-3\">\n                            <Card.Header>User Information</Card.Header>\n                            <Card.Body>\n                                <p><strong>User ID:</strong> {debugInfo.userId}</p>\n                                <p><strong>Email:</strong> {debugInfo.userEmail}</p>\n                                <p><strong>Role from localStorage:</strong> {debugInfo.roleFromLocalStorage}</p>\n                                <p><strong>User Metadata:</strong> {JSON.stringify(debugInfo.userMetadata, null, 2)}</p>\n                            </Card.Body>\n                        </Card>\n\n                        <Card className=\"mb-3\">\n                            <Card.Header>Users Table Data</Card.Header>\n                            <Card.Body>\n                                {debugInfo.usersTableError ? (\n                                    <Alert variant=\"danger\">\n                                        Error: {JSON.stringify(debugInfo.usersTableError, null, 2)}\n                                    </Alert>\n                                ) : (\n                                    <pre>{JSON.stringify(debugInfo.usersTableData, null, 2)}</pre>\n                                )}\n                            </Card.Body>\n                        </Card>\n\n                        <Card className=\"mb-3\">\n                            <Card.Header>Agent Profile Data</Card.Header>\n                            <Card.Body>\n                                {debugInfo.agentProfileError ? (\n                                    <Alert variant=\"danger\">\n                                        Error: {JSON.stringify(debugInfo.agentProfileError, null, 2)}\n                                        {debugInfo.agentProfileError.code === 'PGRST116' && (\n                                            <div className=\"mt-2\">\n                                                <p>No agent profile found. This is likely the cause of the issue.</p>\n                                                <Button onClick={createAgentProfile} variant=\"primary\">\n                                                    Create Agent Profile\n                                                </Button>\n                                            </div>\n                                        )}\n                                    </Alert>\n                                ) : (\n                                    <pre>{JSON.stringify(debugInfo.agentProfileData, null, 2)}</pre>\n                                )}\n                            </Card.Body>\n                        </Card>\n\n                        <Card className=\"mb-3\">\n                            <Card.Header>Maker Profile Data (for reference)</Card.Header>\n                            <Card.Body>\n                                {debugInfo.makerProfileError ? (\n                                    <Alert variant=\"warning\">\n                                        Error: {JSON.stringify(debugInfo.makerProfileError, null, 2)}\n                                    </Alert>\n                                ) : (\n                                    <pre>{JSON.stringify(debugInfo.makerProfileData, null, 2)}</pre>\n                                )}\n                            </Card.Body>\n                        </Card>\n                    </Col>\n                </Row>\n            )}\n        </Container>\n    );\n};\n\nexport default DebugAgent;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,SAAS,CAAEC,GAAG,CAAEC,GAAG,CAAEC,IAAI,CAAEC,MAAM,CAAEC,KAAK,KAAQ,iBAAiB,CAC1E,OAASC,cAAc,KAAQ,eAAe,CAC9C,OAASC,WAAW,KAAQ,sBAAsB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEnD,KAAM,CAAAC,UAAU,CAAGA,CAAA,GAAM,CACrB,KAAM,CAAEC,CAAE,CAAC,CAAGP,cAAc,CAAC,CAAC,CAC9B,KAAM,CAACQ,SAAS,CAAEC,YAAY,CAAC,CAAGjB,QAAQ,CAAC,IAAI,CAAC,CAChD,KAAM,CAACkB,OAAO,CAAEC,UAAU,CAAC,CAAGnB,QAAQ,CAAC,IAAI,CAAC,CAE5CC,SAAS,CAAC,IAAM,CACZ,KAAM,CAAAmB,cAAc,CAAG,KAAAA,CAAA,GAAY,CAC/B,KAAM,CAAAC,QAAQ,CAAGZ,WAAW,CAAC,CAAC,CAC9B,GAAI,CAACY,QAAQ,CAAE,CACXJ,YAAY,CAAC,CAAEK,KAAK,CAAE,0BAA2B,CAAC,CAAC,CACnDH,UAAU,CAAC,KAAK,CAAC,CACjB,OACJ,CAEAA,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAEI,IAAI,CAAE,CAAEC,IAAK,CAAE,CAAC,CAAG,KAAM,CAAAH,QAAQ,CAACI,IAAI,CAACC,OAAO,CAAC,CAAC,CAExD,GAAI,CAACF,IAAI,CAAE,CACPP,YAAY,CAAC,CAAEK,KAAK,CAAE,oBAAqB,CAAC,CAAC,CAC7CH,UAAU,CAAC,KAAK,CAAC,CACjB,OACJ,CAEA,KAAM,CAAAQ,IAAI,CAAG,CACTC,MAAM,CAAEJ,IAAI,CAACK,EAAE,CACfC,SAAS,CAAEN,IAAI,CAACO,KAAK,CACrBC,oBAAoB,CAAEC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC,CACvDC,YAAY,CAAEX,IAAI,CAACY,aACvB,CAAC,CAED;AACA,GAAI,CACA,KAAM,CAAEb,IAAI,CAAEc,QAAQ,CAAEf,KAAK,CAAEgB,SAAU,CAAC,CAAG,KAAM,CAAAjB,QAAQ,CACtDkB,IAAI,CAAC,OAAO,CAAC,CACbC,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,IAAI,CAAEjB,IAAI,CAACK,EAAE,CAAC,CACjBa,MAAM,CAAC,CAAC,CAEbf,IAAI,CAACgB,cAAc,CAAGN,QAAQ,CAC9BV,IAAI,CAACiB,eAAe,CAAGN,SAAS,CACpC,CAAE,MAAOO,GAAG,CAAE,CACVlB,IAAI,CAACiB,eAAe,CAAGC,GAAG,CAC9B,CAEA;AACA,GAAI,CACA,KAAM,CAAEtB,IAAI,CAAEuB,SAAS,CAAExB,KAAK,CAAEyB,UAAW,CAAC,CAAG,KAAM,CAAA1B,QAAQ,CACxDkB,IAAI,CAAC,gBAAgB,CAAC,CACtBC,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,SAAS,CAAEjB,IAAI,CAACK,EAAE,CAAC,CACtBa,MAAM,CAAC,CAAC,CAEbf,IAAI,CAACqB,gBAAgB,CAAGF,SAAS,CACjCnB,IAAI,CAACsB,iBAAiB,CAAGF,UAAU,CACvC,CAAE,MAAOF,GAAG,CAAE,CACVlB,IAAI,CAACsB,iBAAiB,CAAGJ,GAAG,CAChC,CAEA;AACA,GAAI,CACA,KAAM,CAAEtB,IAAI,CAAE2B,SAAS,CAAE5B,KAAK,CAAE6B,UAAW,CAAC,CAAG,KAAM,CAAA9B,QAAQ,CACxDkB,IAAI,CAAC,gBAAgB,CAAC,CACtBC,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,SAAS,CAAEjB,IAAI,CAACK,EAAE,CAAC,CACtBa,MAAM,CAAC,CAAC,CAEbf,IAAI,CAACyB,gBAAgB,CAAGF,SAAS,CACjCvB,IAAI,CAAC0B,iBAAiB,CAAGF,UAAU,CACvC,CAAE,MAAON,GAAG,CAAE,CACVlB,IAAI,CAAC0B,iBAAiB,CAAGR,GAAG,CAChC,CAEA5B,YAAY,CAACU,IAAI,CAAC,CAClBR,UAAU,CAAC,KAAK,CAAC,CACrB,CAAC,CAEDC,cAAc,CAAC,CAAC,CACpB,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAkC,kBAAkB,CAAG,KAAAA,CAAA,GAAY,CACnC,KAAM,CAAAjC,QAAQ,CAAGZ,WAAW,CAAC,CAAC,CAC9B,GAAI,CAACY,QAAQ,EAAI,CAACL,SAAS,CAAE,OAE7B,GAAI,CACA,KAAM,CAAEO,IAAI,CAAED,KAAM,CAAC,CAAG,KAAM,CAAAD,QAAQ,CACjCkB,IAAI,CAAC,gBAAgB,CAAC,CACtBgB,MAAM,CAAC,CACJ,CACIC,OAAO,CAAExC,SAAS,CAACY,MAAM,CACzB6B,UAAU,CAAE,eAAe,CAC3BC,cAAc,CAAE,IAAI,CACpBC,UAAU,CAAE,SAChB,CAAC,CACJ,CAAC,CACDnB,MAAM,CAAC,CAAC,CACRE,MAAM,CAAC,CAAC,CAEb,GAAIpB,KAAK,CAAE,CACPsC,KAAK,CAAC,gCAAgC,CAAGtC,KAAK,CAACuC,OAAO,CAAC,CAC3D,CAAC,IAAM,CACHD,KAAK,CAAC,qCAAqC,CAAC,CAC5CE,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC,CAC5B,CACJ,CAAE,MAAOnB,GAAG,CAAE,CACVe,KAAK,CAAC,SAAS,CAAGf,GAAG,CAACgB,OAAO,CAAC,CAClC,CACJ,CAAC,CAED,GAAI3C,OAAO,CAAE,CACT,mBAAOP,IAAA,QAAAsD,QAAA,CAAK,8BAA4B,CAAK,CAAC,CAClD,CAEA,mBACIpD,KAAA,CAACX,SAAS,EAACgE,KAAK,MAAAD,QAAA,eACZtD,IAAA,CAACR,GAAG,EAACgE,SAAS,CAAC,MAAM,CAAAF,QAAA,cACjBtD,IAAA,CAACP,GAAG,EAAA6D,QAAA,cACAtD,IAAA,OAAAsD,QAAA,CAAI,yBAAuB,CAAI,CAAC,CAC/B,CAAC,CACL,CAAC,CAELjD,SAAS,eACNL,IAAA,CAACR,GAAG,EAAA8D,QAAA,cACApD,KAAA,CAACT,GAAG,EAAA6D,QAAA,eACApD,KAAA,CAACR,IAAI,EAAC8D,SAAS,CAAC,MAAM,CAAAF,QAAA,eAClBtD,IAAA,CAACN,IAAI,CAAC+D,MAAM,EAAAH,QAAA,CAAC,kBAAgB,CAAa,CAAC,cAC3CpD,KAAA,CAACR,IAAI,CAACgE,IAAI,EAAAJ,QAAA,eACNpD,KAAA,MAAAoD,QAAA,eAAGtD,IAAA,WAAAsD,QAAA,CAAQ,UAAQ,CAAQ,CAAC,IAAC,CAACjD,SAAS,CAACY,MAAM,EAAI,CAAC,cACnDf,KAAA,MAAAoD,QAAA,eAAGtD,IAAA,WAAAsD,QAAA,CAAQ,QAAM,CAAQ,CAAC,IAAC,CAACjD,SAAS,CAACc,SAAS,EAAI,CAAC,cACpDjB,KAAA,MAAAoD,QAAA,eAAGtD,IAAA,WAAAsD,QAAA,CAAQ,yBAAuB,CAAQ,CAAC,IAAC,CAACjD,SAAS,CAACgB,oBAAoB,EAAI,CAAC,cAChFnB,KAAA,MAAAoD,QAAA,eAAGtD,IAAA,WAAAsD,QAAA,CAAQ,gBAAc,CAAQ,CAAC,IAAC,CAACK,IAAI,CAACC,SAAS,CAACvD,SAAS,CAACmB,YAAY,CAAE,IAAI,CAAE,CAAC,CAAC,EAAI,CAAC,EACjF,CAAC,EACV,CAAC,cAEPtB,KAAA,CAACR,IAAI,EAAC8D,SAAS,CAAC,MAAM,CAAAF,QAAA,eAClBtD,IAAA,CAACN,IAAI,CAAC+D,MAAM,EAAAH,QAAA,CAAC,kBAAgB,CAAa,CAAC,cAC3CtD,IAAA,CAACN,IAAI,CAACgE,IAAI,EAAAJ,QAAA,CACLjD,SAAS,CAAC4B,eAAe,cACtB/B,KAAA,CAACN,KAAK,EAACiE,OAAO,CAAC,QAAQ,CAAAP,QAAA,EAAC,SACb,CAACK,IAAI,CAACC,SAAS,CAACvD,SAAS,CAAC4B,eAAe,CAAE,IAAI,CAAE,CAAC,CAAC,EACvD,CAAC,cAERjC,IAAA,QAAAsD,QAAA,CAAMK,IAAI,CAACC,SAAS,CAACvD,SAAS,CAAC2B,cAAc,CAAE,IAAI,CAAE,CAAC,CAAC,CAAM,CAChE,CACM,CAAC,EACV,CAAC,cAEP9B,KAAA,CAACR,IAAI,EAAC8D,SAAS,CAAC,MAAM,CAAAF,QAAA,eAClBtD,IAAA,CAACN,IAAI,CAAC+D,MAAM,EAAAH,QAAA,CAAC,oBAAkB,CAAa,CAAC,cAC7CtD,IAAA,CAACN,IAAI,CAACgE,IAAI,EAAAJ,QAAA,CACLjD,SAAS,CAACiC,iBAAiB,cACxBpC,KAAA,CAACN,KAAK,EAACiE,OAAO,CAAC,QAAQ,CAAAP,QAAA,EAAC,SACb,CAACK,IAAI,CAACC,SAAS,CAACvD,SAAS,CAACiC,iBAAiB,CAAE,IAAI,CAAE,CAAC,CAAC,CAC3DjC,SAAS,CAACiC,iBAAiB,CAACwB,IAAI,GAAK,UAAU,eAC5C5D,KAAA,QAAKsD,SAAS,CAAC,MAAM,CAAAF,QAAA,eACjBtD,IAAA,MAAAsD,QAAA,CAAG,gEAA8D,CAAG,CAAC,cACrEtD,IAAA,CAACL,MAAM,EAACoE,OAAO,CAAEpB,kBAAmB,CAACkB,OAAO,CAAC,SAAS,CAAAP,QAAA,CAAC,sBAEvD,CAAQ,CAAC,EACR,CACR,EACE,CAAC,cAERtD,IAAA,QAAAsD,QAAA,CAAMK,IAAI,CAACC,SAAS,CAACvD,SAAS,CAACgC,gBAAgB,CAAE,IAAI,CAAE,CAAC,CAAC,CAAM,CAClE,CACM,CAAC,EACV,CAAC,cAEPnC,KAAA,CAACR,IAAI,EAAC8D,SAAS,CAAC,MAAM,CAAAF,QAAA,eAClBtD,IAAA,CAACN,IAAI,CAAC+D,MAAM,EAAAH,QAAA,CAAC,oCAAkC,CAAa,CAAC,cAC7DtD,IAAA,CAACN,IAAI,CAACgE,IAAI,EAAAJ,QAAA,CACLjD,SAAS,CAACqC,iBAAiB,cACxBxC,KAAA,CAACN,KAAK,EAACiE,OAAO,CAAC,SAAS,CAAAP,QAAA,EAAC,SACd,CAACK,IAAI,CAACC,SAAS,CAACvD,SAAS,CAACqC,iBAAiB,CAAE,IAAI,CAAE,CAAC,CAAC,EACzD,CAAC,cAER1C,IAAA,QAAAsD,QAAA,CAAMK,IAAI,CAACC,SAAS,CAACvD,SAAS,CAACoC,gBAAgB,CAAE,IAAI,CAAE,CAAC,CAAC,CAAM,CAClE,CACM,CAAC,EACV,CAAC,EACN,CAAC,CACL,CACR,EACM,CAAC,CAEpB,CAAC,CAED,cAAe,CAAAtC,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[505],{505:(e,s,r)=>{r.r(s),r.d(s,{default:()=>x});var a=r(5043),t=r(3519),l=r(1072),n=r(8602),c=r(8628),o=r(4282),d=r(4117),i=r(1283),m=r(4312),f=r(579);const x=()=>{const{t:e}=(0,d.Bd)(),[s,r]=(0,a.useState)(null),[x,h]=(0,a.useState)(!0);return(0,a.useEffect)(()=>{(async()=>{const e=(0,m.b)();if(!e)return;h(!0);const{data:{user:s}}=await e.auth.getUser();if(!s)return console.log("No user found"),void h(!1);console.log("Current user:",s.id),console.log("User role from localStorage:",localStorage.getItem("user_role"));const{data:a,error:t}=await e.from("agent_profiles").select("*, maker_profiles(brand_name)").eq("user_id",s.id).single();t?(console.error("Error fetching agent profile:",t),console.error("User ID:",s.id),console.error("Error details:",t)):(console.log("Agent profile data:",a),r(a)),h(!1)})()},[]),x?(0,f.jsx)("div",{children:e("loading_agent_dashboard")}):s?(0,f.jsxs)(t.A,{fluid:!0,children:[(0,f.jsx)(l.A,{className:"mb-3",children:(0,f.jsx)(n.A,{children:(0,f.jsx)("h2",{children:e("agent_dashboard")})})}),(0,f.jsxs)(l.A,{children:[(0,f.jsx)(n.A,{md:4,children:(0,f.jsx)(c.A,{className:"bg-primary text-white mb-3",children:(0,f.jsxs)(c.A.Body,{children:[(0,f.jsx)(c.A.Title,{children:e("brand_name")}),(0,f.jsx)("h3",{children:s.brand_name||"N/A"})]})})}),(0,f.jsx)(n.A,{md:4,children:(0,f.jsx)(c.A,{className:"bg-success text-white mb-3",children:(0,f.jsxs)(c.A.Body,{children:[(0,f.jsx)(c.A.Title,{children:e("commission_rate")}),(0,f.jsx)("h3",{children:s.commission_pct?100*s.commission_pct+"%":"N/A"})]})})}),(0,f.jsx)(n.A,{md:4,children:(0,f.jsx)(c.A,{className:"bg-info text-white mb-3",children:(0,f.jsxs)(c.A.Body,{children:[(0,f.jsx)(c.A.Title,{children:e("kyc_status")}),(0,f.jsx)("h3",{children:s.kyc_status||"N/A"})]})})})]}),(0,f.jsxs)(l.A,{className:"mt-4",children:[(0,f.jsx)(n.A,{md:6,className:"text-center",children:(0,f.jsx)(c.A,{children:(0,f.jsxs)(c.A.Body,{children:[(0,f.jsx)("h4",{children:e("member_management")}),(0,f.jsx)("p",{children:e("my_subordinate_members")}),(0,f.jsx)(o.A,{as:i.N_,to:"/agent/members",variant:"primary",children:e("enter_member_list")})]})})}),(0,f.jsx)(n.A,{md:6,className:"text-center",children:(0,f.jsx)(c.A,{children:(0,f.jsxs)(c.A.Body,{children:[(0,f.jsx)("h4",{children:e("product_management")}),(0,f.jsx)("p",{children:e("products_on_sale")}),(0,f.jsx)(o.A,{as:i.N_,to:"/agent/products",variant:"success",children:e("browse_agent_products")})]})})})]})]}):(0,f.jsx)("div",{className:"alert alert-warning",children:e("not_agent")})}},1072:(e,s,r)=>{r.d(s,{A:()=>d});var a=r(8139),t=r.n(a),l=r(5043),n=r(7852),c=r(579);const o=l.forwardRef((e,s)=>{let{bsPrefix:r,className:a,as:l="div",...o}=e;const d=(0,n.oU)(r,"row"),i=(0,n.gy)(),m=(0,n.Jm)(),f=`${d}-cols`,x=[];return i.forEach(e=>{const s=o[e];let r;delete o[e],null!=s&&"object"===typeof s?({cols:r}=s):r=s;const a=e!==m?`-${e}`:"";null!=r&&x.push(`${f}${a}-${r}`)}),(0,c.jsx)(l,{ref:s,...o,className:t()(a,d,...x)})});o.displayName="Row";const d=o},8602:(e,s,r)=>{r.d(s,{A:()=>d});var a=r(8139),t=r.n(a),l=r(5043),n=r(7852),c=r(579);const o=l.forwardRef((e,s)=>{const[{className:r,...a},{as:l="div",bsPrefix:o,spans:d}]=function(e){let{as:s,bsPrefix:r,className:a,...l}=e;r=(0,n.oU)(r,"col");const c=(0,n.gy)(),o=(0,n.Jm)(),d=[],i=[];return c.forEach(e=>{const s=l[e];let a,t,n;delete l[e],"object"===typeof s&&null!=s?({span:a,offset:t,order:n}=s):a=s;const c=e!==o?`-${e}`:"";a&&d.push(!0===a?`${r}${c}`:`${r}${c}-${a}`),null!=n&&i.push(`order${c}-${n}`),null!=t&&i.push(`offset${c}-${t}`)}),[{...l,className:t()(a,...d,...i)},{as:s,bsPrefix:r,spans:d}]}(e);return(0,c.jsx)(l,{...a,ref:s,className:t()(r,!d.length&&o)})});o.displayName="Col";const d=o},8628:(e,s,r)=>{r.d(s,{A:()=>k});var a=r(8139),t=r.n(a),l=r(5043),n=r(7852),c=r(579);const o=l.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:l="div",...o}=e;return a=(0,n.oU)(a,"card-body"),(0,c.jsx)(l,{ref:s,className:t()(r,a),...o})});o.displayName="CardBody";const d=o,i=l.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:l="div",...o}=e;return a=(0,n.oU)(a,"card-footer"),(0,c.jsx)(l,{ref:s,className:t()(r,a),...o})});i.displayName="CardFooter";const m=i;var f=r(1778);const x=l.forwardRef((e,s)=>{let{bsPrefix:r,className:a,as:o="div",...d}=e;const i=(0,n.oU)(r,"card-header"),m=(0,l.useMemo)(()=>({cardHeaderBsPrefix:i}),[i]);return(0,c.jsx)(f.A.Provider,{value:m,children:(0,c.jsx)(o,{ref:s,...d,className:t()(a,i)})})});x.displayName="CardHeader";const h=x,u=l.forwardRef((e,s)=>{let{bsPrefix:r,className:a,variant:l,as:o="img",...d}=e;const i=(0,n.oU)(r,"card-img");return(0,c.jsx)(o,{ref:s,className:t()(l?`${i}-${l}`:i,a),...d})});u.displayName="CardImg";const j=u,N=l.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:l="div",...o}=e;return a=(0,n.oU)(a,"card-img-overlay"),(0,c.jsx)(l,{ref:s,className:t()(r,a),...o})});N.displayName="CardImgOverlay";const b=N,g=l.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:l="a",...o}=e;return a=(0,n.oU)(a,"card-link"),(0,c.jsx)(l,{ref:s,className:t()(r,a),...o})});g.displayName="CardLink";const p=g;var A=r(4488);const y=(0,A.A)("h6"),_=l.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:l=y,...o}=e;return a=(0,n.oU)(a,"card-subtitle"),(0,c.jsx)(l,{ref:s,className:t()(r,a),...o})});_.displayName="CardSubtitle";const v=_,w=l.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:l="p",...o}=e;return a=(0,n.oU)(a,"card-text"),(0,c.jsx)(l,{ref:s,className:t()(r,a),...o})});w.displayName="CardText";const $=w,P=(0,A.A)("h5"),U=l.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:l=P,...o}=e;return a=(0,n.oU)(a,"card-title"),(0,c.jsx)(l,{ref:s,className:t()(r,a),...o})});U.displayName="CardTitle";const C=U,R=l.forwardRef((e,s)=>{let{bsPrefix:r,className:a,bg:l,text:o,border:i,body:m=!1,children:f,as:x="div",...h}=e;const u=(0,n.oU)(r,"card");return(0,c.jsx)(x,{ref:s,...h,className:t()(a,u,l&&`bg-${l}`,o&&`text-${o}`,i&&`border-${i}`),children:m?(0,c.jsx)(d,{children:f}):f})});R.displayName="Card";const k=Object.assign(R,{Img:j,Title:C,Subtitle:v,Body:d,Link:p,Text:$,Header:h,Footer:m,ImgOverlay:b})}}]);
//# sourceMappingURL=505.f6bc5953.chunk.js.map
{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Container,Row,Col,Card,Table,Badge}from'react-bootstrap';import{getSupabase}from'../../supabaseClient';import{useTranslation}from'react-i18next';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ManualDeposits=()=>{const{t}=useTranslation();const[journals,setJournals]=useState([]);const[loading,setLoading]=useState(true);useEffect(()=>{const fetchJournals=async()=>{const supabase=getSupabase();if(!supabase)return;setLoading(true);const{data:{user}}=await supabase.auth.getUser();if(!user){setLoading(false);return;// User not logged in\n}// Fetch manual journals with maker, customer and currency information\nconst{data,error}=await supabase.from('manual_journals').select(`\n                    id,\n                    amount,\n                    journal_type,\n                    remark,\n                    created_at,\n                    maker_profiles (\n                        domain,\n                        users (\n                            email\n                        )\n                    ),\n                    customer_profiles (\n                        real_name,\n                        users (\n                            email\n                        )\n                    ),\n                    currencies (\n                        code,\n                        total_supply,\n                        withdrawable\n                    )\n                `).order('created_at',{ascending:false});if(error){console.error('Error fetching manual journals:',error);}else{setJournals(data);}setLoading(false);};fetchJournals();},[]);const getJournalTypeBadge=type=>{switch(type){case'deposit':return/*#__PURE__*/_jsx(Badge,{bg:\"success\",children:t('deposit')});case'withdrawal':return/*#__PURE__*/_jsx(Badge,{bg:\"danger\",children:t('withdrawal')});case'adjustment':return/*#__PURE__*/_jsx(Badge,{bg:\"warning\",children:t('adjustment')});case'bonus':return/*#__PURE__*/_jsx(Badge,{bg:\"info\",children:t('bonus')});case'penalty':return/*#__PURE__*/_jsx(Badge,{bg:\"dark\",children:t('penalty')});default:return/*#__PURE__*/_jsx(Badge,{bg:\"secondary\",children:type||'-'});}};const formatAmount=amount=>{if(!amount)return'0.000000';return parseFloat(amount).toFixed(6);};if(loading){return/*#__PURE__*/_jsx(\"div\",{children:t('loading_manual_deposits')});}return/*#__PURE__*/_jsxs(Container,{children:[/*#__PURE__*/_jsx(\"h2\",{className:\"mb-4\",children:t('manual_deposit')}),/*#__PURE__*/_jsx(Row,{children:/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Card.Body,{children:/*#__PURE__*/_jsxs(Table,{striped:true,bordered:true,hover:true,responsive:true,children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{children:t('journal_id')}),/*#__PURE__*/_jsx(\"th\",{children:t('maker')}),/*#__PURE__*/_jsx(\"th\",{children:t('customer')}),/*#__PURE__*/_jsx(\"th\",{children:t('currency_code')}),/*#__PURE__*/_jsx(\"th\",{children:t('amount')}),/*#__PURE__*/_jsx(\"th\",{children:t('journal_type')}),/*#__PURE__*/_jsx(\"th\",{children:t('remark')}),/*#__PURE__*/_jsx(\"th\",{children:t('created_at')})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:journals.length===0?/*#__PURE__*/_jsx(\"tr\",{children:/*#__PURE__*/_jsx(\"td\",{colSpan:\"8\",className:\"text-center\",children:t('no_manual_deposits_available')})}):journals.map(journal=>{var _journal$maker_profil,_journal$maker_profil2,_journal$maker_profil3,_journal$customer_pro,_journal$customer_pro2,_journal$customer_pro3,_journal$currencies;return/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{children:journal.id}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{children:((_journal$maker_profil=journal.maker_profiles)===null||_journal$maker_profil===void 0?void 0:_journal$maker_profil.domain)||'-'}),/*#__PURE__*/_jsx(\"small\",{className:\"text-muted\",children:((_journal$maker_profil2=journal.maker_profiles)===null||_journal$maker_profil2===void 0?void 0:(_journal$maker_profil3=_journal$maker_profil2.users)===null||_journal$maker_profil3===void 0?void 0:_journal$maker_profil3.email)||'-'})]})}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{children:((_journal$customer_pro=journal.customer_profiles)===null||_journal$customer_pro===void 0?void 0:_journal$customer_pro.real_name)||'-'}),/*#__PURE__*/_jsx(\"small\",{className:\"text-muted\",children:((_journal$customer_pro2=journal.customer_profiles)===null||_journal$customer_pro2===void 0?void 0:(_journal$customer_pro3=_journal$customer_pro2.users)===null||_journal$customer_pro3===void 0?void 0:_journal$customer_pro3.email)||'-'})]})}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(Badge,{bg:\"primary\",children:((_journal$currencies=journal.currencies)===null||_journal$currencies===void 0?void 0:_journal$currencies.code)||'-'})}),/*#__PURE__*/_jsxs(\"td\",{className:journal.amount>=0?'text-success':'text-danger',children:[journal.amount>=0?'+':'',formatAmount(journal.amount)]}),/*#__PURE__*/_jsx(\"td\",{children:getJournalTypeBadge(journal.journal_type)}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(\"div\",{style:{maxWidth:'200px',wordWrap:'break-word'},children:journal.remark||'-'})}),/*#__PURE__*/_jsx(\"td\",{children:new Date(journal.created_at).toLocaleString()})]},journal.id);})})]})})})})})]});};export default ManualDeposits;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "Card", "Table", "Badge", "getSupabase", "useTranslation", "jsx", "_jsx", "jsxs", "_jsxs", "ManualDeposits", "t", "journals", "setJournals", "loading", "setLoading", "fetchJournals", "supabase", "data", "user", "auth", "getUser", "error", "from", "select", "order", "ascending", "console", "getJournalTypeBadge", "type", "bg", "children", "formatAmount", "amount", "parseFloat", "toFixed", "className", "Body", "striped", "bordered", "hover", "responsive", "length", "colSpan", "map", "journal", "_journal$maker_profil", "_journal$maker_profil2", "_journal$maker_profil3", "_journal$customer_pro", "_journal$customer_pro2", "_journal$customer_pro3", "_journal$currencies", "id", "maker_profiles", "domain", "users", "email", "customer_profiles", "real_name", "currencies", "code", "journal_type", "style", "max<PERSON><PERSON><PERSON>", "wordWrap", "remark", "Date", "created_at", "toLocaleString"], "sources": ["D:/New_System/fil-platform-plugin/frontend/src/pages/maker/ManualDeposits.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Table, Badge } from 'react-bootstrap';\nimport { getSupabase } from '../../supabaseClient';\nimport { useTranslation } from 'react-i18next';\n\nconst ManualDeposits = () => {\n    const { t } = useTranslation();\n    const [journals, setJournals] = useState([]);\n    const [loading, setLoading] = useState(true);\n\n    useEffect(() => {\n        const fetchJournals = async () => {\n            const supabase = getSupabase();\n            if (!supabase) return;\n\n            setLoading(true);\n            const { data: { user } } = await supabase.auth.getUser();\n\n            if (!user) {\n                setLoading(false);\n                return; // User not logged in\n            }\n\n            // Fetch manual journals with maker, customer and currency information\n            const { data, error } = await supabase\n                .from('manual_journals')\n                .select(`\n                    id,\n                    amount,\n                    journal_type,\n                    remark,\n                    created_at,\n                    maker_profiles (\n                        domain,\n                        users (\n                            email\n                        )\n                    ),\n                    customer_profiles (\n                        real_name,\n                        users (\n                            email\n                        )\n                    ),\n                    currencies (\n                        code,\n                        total_supply,\n                        withdrawable\n                    )\n                `)\n                .order('created_at', { ascending: false });\n\n            if (error) {\n                console.error('Error fetching manual journals:', error);\n            } else {\n                setJournals(data);\n            }\n            setLoading(false);\n        };\n\n        fetchJournals();\n    }, []);\n\n    const getJournalTypeBadge = (type) => {\n        switch (type) {\n            case 'deposit':\n                return <Badge bg=\"success\">{t('deposit')}</Badge>;\n            case 'withdrawal':\n                return <Badge bg=\"danger\">{t('withdrawal')}</Badge>;\n            case 'adjustment':\n                return <Badge bg=\"warning\">{t('adjustment')}</Badge>;\n            case 'bonus':\n                return <Badge bg=\"info\">{t('bonus')}</Badge>;\n            case 'penalty':\n                return <Badge bg=\"dark\">{t('penalty')}</Badge>;\n            default:\n                return <Badge bg=\"secondary\">{type || '-'}</Badge>;\n        }\n    };\n\n    const formatAmount = (amount) => {\n        if (!amount) return '0.000000';\n        return parseFloat(amount).toFixed(6);\n    };\n\n    if (loading) {\n        return <div>{t('loading_manual_deposits')}</div>;\n    }\n\n    return (\n        <Container>\n            <h2 className=\"mb-4\">{t('manual_deposit')}</h2>\n            <Row>\n                <Col>\n                    <Card>\n                        <Card.Body>\n                            <Table striped bordered hover responsive>\n                                <thead>\n                                    <tr>\n                                        <th>{t('journal_id')}</th>\n                                        <th>{t('maker')}</th>\n                                        <th>{t('customer')}</th>\n                                        <th>{t('currency_code')}</th>\n                                        <th>{t('amount')}</th>\n                                        <th>{t('journal_type')}</th>\n                                        <th>{t('remark')}</th>\n                                        <th>{t('created_at')}</th>\n                                    </tr>\n                                </thead>\n                                <tbody>\n                                    {journals.length === 0 ? (\n                                        <tr>\n                                            <td colSpan=\"8\" className=\"text-center\">{t('no_manual_deposits_available')}</td>\n                                        </tr>\n                                    ) : (\n                                        journals.map(journal => (\n                                            <tr key={journal.id}>\n                                                <td>{journal.id}</td>\n                                                <td>\n                                                    <div>\n                                                        <div>{journal.maker_profiles?.domain || '-'}</div>\n                                                        <small className=\"text-muted\">\n                                                            {journal.maker_profiles?.users?.email || '-'}\n                                                        </small>\n                                                    </div>\n                                                </td>\n                                                <td>\n                                                    <div>\n                                                        <div>{journal.customer_profiles?.real_name || '-'}</div>\n                                                        <small className=\"text-muted\">\n                                                            {journal.customer_profiles?.users?.email || '-'}\n                                                        </small>\n                                                    </div>\n                                                </td>\n                                                <td>\n                                                    <Badge bg=\"primary\">\n                                                        {journal.currencies?.code || '-'}\n                                                    </Badge>\n                                                </td>\n                                                <td className={journal.amount >= 0 ? 'text-success' : 'text-danger'}>\n                                                    {journal.amount >= 0 ? '+' : ''}{formatAmount(journal.amount)}\n                                                </td>\n                                                <td>{getJournalTypeBadge(journal.journal_type)}</td>\n                                                <td>\n                                                    <div style={{ maxWidth: '200px', wordWrap: 'break-word' }}>\n                                                        {journal.remark || '-'}\n                                                    </div>\n                                                </td>\n                                                <td>{new Date(journal.created_at).toLocaleString()}</td>\n                                            </tr>\n                                        ))\n                                    )}\n                                </tbody>\n                            </Table>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n        </Container>\n    );\n};\n\nexport default ManualDeposits;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,SAAS,CAAEC,GAAG,CAAEC,GAAG,CAAEC,IAAI,CAAEC,KAAK,CAAEC,KAAK,KAAQ,iBAAiB,CACzE,OAASC,WAAW,KAAQ,sBAAsB,CAClD,OAASC,cAAc,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE/C,KAAM,CAAAC,cAAc,CAAGA,CAAA,GAAM,CACzB,KAAM,CAAEC,CAAE,CAAC,CAAGN,cAAc,CAAC,CAAC,CAC9B,KAAM,CAACO,QAAQ,CAAEC,WAAW,CAAC,CAAGjB,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACkB,OAAO,CAAEC,UAAU,CAAC,CAAGnB,QAAQ,CAAC,IAAI,CAAC,CAE5CC,SAAS,CAAC,IAAM,CACZ,KAAM,CAAAmB,aAAa,CAAG,KAAAA,CAAA,GAAY,CAC9B,KAAM,CAAAC,QAAQ,CAAGb,WAAW,CAAC,CAAC,CAC9B,GAAI,CAACa,QAAQ,CAAE,OAEfF,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAEG,IAAI,CAAE,CAAEC,IAAK,CAAE,CAAC,CAAG,KAAM,CAAAF,QAAQ,CAACG,IAAI,CAACC,OAAO,CAAC,CAAC,CAExD,GAAI,CAACF,IAAI,CAAE,CACPJ,UAAU,CAAC,KAAK,CAAC,CACjB,OAAQ;AACZ,CAEA;AACA,KAAM,CAAEG,IAAI,CAAEI,KAAM,CAAC,CAAG,KAAM,CAAAL,QAAQ,CACjCM,IAAI,CAAC,iBAAiB,CAAC,CACvBC,MAAM,CAAC;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,CAAC,CACDC,KAAK,CAAC,YAAY,CAAE,CAAEC,SAAS,CAAE,KAAM,CAAC,CAAC,CAE9C,GAAIJ,KAAK,CAAE,CACPK,OAAO,CAACL,KAAK,CAAC,iCAAiC,CAAEA,KAAK,CAAC,CAC3D,CAAC,IAAM,CACHT,WAAW,CAACK,IAAI,CAAC,CACrB,CACAH,UAAU,CAAC,KAAK,CAAC,CACrB,CAAC,CAEDC,aAAa,CAAC,CAAC,CACnB,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAY,mBAAmB,CAAIC,IAAI,EAAK,CAClC,OAAQA,IAAI,EACR,IAAK,SAAS,CACV,mBAAOtB,IAAA,CAACJ,KAAK,EAAC2B,EAAE,CAAC,SAAS,CAAAC,QAAA,CAAEpB,CAAC,CAAC,SAAS,CAAC,CAAQ,CAAC,CACrD,IAAK,YAAY,CACb,mBAAOJ,IAAA,CAACJ,KAAK,EAAC2B,EAAE,CAAC,QAAQ,CAAAC,QAAA,CAAEpB,CAAC,CAAC,YAAY,CAAC,CAAQ,CAAC,CACvD,IAAK,YAAY,CACb,mBAAOJ,IAAA,CAACJ,KAAK,EAAC2B,EAAE,CAAC,SAAS,CAAAC,QAAA,CAAEpB,CAAC,CAAC,YAAY,CAAC,CAAQ,CAAC,CACxD,IAAK,OAAO,CACR,mBAAOJ,IAAA,CAACJ,KAAK,EAAC2B,EAAE,CAAC,MAAM,CAAAC,QAAA,CAAEpB,CAAC,CAAC,OAAO,CAAC,CAAQ,CAAC,CAChD,IAAK,SAAS,CACV,mBAAOJ,IAAA,CAACJ,KAAK,EAAC2B,EAAE,CAAC,MAAM,CAAAC,QAAA,CAAEpB,CAAC,CAAC,SAAS,CAAC,CAAQ,CAAC,CAClD,QACI,mBAAOJ,IAAA,CAACJ,KAAK,EAAC2B,EAAE,CAAC,WAAW,CAAAC,QAAA,CAAEF,IAAI,EAAI,GAAG,CAAQ,CAAC,CAC1D,CACJ,CAAC,CAED,KAAM,CAAAG,YAAY,CAAIC,MAAM,EAAK,CAC7B,GAAI,CAACA,MAAM,CAAE,MAAO,UAAU,CAC9B,MAAO,CAAAC,UAAU,CAACD,MAAM,CAAC,CAACE,OAAO,CAAC,CAAC,CAAC,CACxC,CAAC,CAED,GAAIrB,OAAO,CAAE,CACT,mBAAOP,IAAA,QAAAwB,QAAA,CAAMpB,CAAC,CAAC,yBAAyB,CAAC,CAAM,CAAC,CACpD,CAEA,mBACIF,KAAA,CAACX,SAAS,EAAAiC,QAAA,eACNxB,IAAA,OAAI6B,SAAS,CAAC,MAAM,CAAAL,QAAA,CAAEpB,CAAC,CAAC,gBAAgB,CAAC,CAAK,CAAC,cAC/CJ,IAAA,CAACR,GAAG,EAAAgC,QAAA,cACAxB,IAAA,CAACP,GAAG,EAAA+B,QAAA,cACAxB,IAAA,CAACN,IAAI,EAAA8B,QAAA,cACDxB,IAAA,CAACN,IAAI,CAACoC,IAAI,EAAAN,QAAA,cACNtB,KAAA,CAACP,KAAK,EAACoC,OAAO,MAACC,QAAQ,MAACC,KAAK,MAACC,UAAU,MAAAV,QAAA,eACpCxB,IAAA,UAAAwB,QAAA,cACItB,KAAA,OAAAsB,QAAA,eACIxB,IAAA,OAAAwB,QAAA,CAAKpB,CAAC,CAAC,YAAY,CAAC,CAAK,CAAC,cAC1BJ,IAAA,OAAAwB,QAAA,CAAKpB,CAAC,CAAC,OAAO,CAAC,CAAK,CAAC,cACrBJ,IAAA,OAAAwB,QAAA,CAAKpB,CAAC,CAAC,UAAU,CAAC,CAAK,CAAC,cACxBJ,IAAA,OAAAwB,QAAA,CAAKpB,CAAC,CAAC,eAAe,CAAC,CAAK,CAAC,cAC7BJ,IAAA,OAAAwB,QAAA,CAAKpB,CAAC,CAAC,QAAQ,CAAC,CAAK,CAAC,cACtBJ,IAAA,OAAAwB,QAAA,CAAKpB,CAAC,CAAC,cAAc,CAAC,CAAK,CAAC,cAC5BJ,IAAA,OAAAwB,QAAA,CAAKpB,CAAC,CAAC,QAAQ,CAAC,CAAK,CAAC,cACtBJ,IAAA,OAAAwB,QAAA,CAAKpB,CAAC,CAAC,YAAY,CAAC,CAAK,CAAC,EAC1B,CAAC,CACF,CAAC,cACRJ,IAAA,UAAAwB,QAAA,CACKnB,QAAQ,CAAC8B,MAAM,GAAK,CAAC,cAClBnC,IAAA,OAAAwB,QAAA,cACIxB,IAAA,OAAIoC,OAAO,CAAC,GAAG,CAACP,SAAS,CAAC,aAAa,CAAAL,QAAA,CAAEpB,CAAC,CAAC,8BAA8B,CAAC,CAAK,CAAC,CAChF,CAAC,CAELC,QAAQ,CAACgC,GAAG,CAACC,OAAO,OAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,mBAAA,oBAChB3C,KAAA,OAAAsB,QAAA,eACIxB,IAAA,OAAAwB,QAAA,CAAKc,OAAO,CAACQ,EAAE,CAAK,CAAC,cACrB9C,IAAA,OAAAwB,QAAA,cACItB,KAAA,QAAAsB,QAAA,eACIxB,IAAA,QAAAwB,QAAA,CAAM,EAAAe,qBAAA,CAAAD,OAAO,CAACS,cAAc,UAAAR,qBAAA,iBAAtBA,qBAAA,CAAwBS,MAAM,GAAI,GAAG,CAAM,CAAC,cAClDhD,IAAA,UAAO6B,SAAS,CAAC,YAAY,CAAAL,QAAA,CACxB,EAAAgB,sBAAA,CAAAF,OAAO,CAACS,cAAc,UAAAP,sBAAA,kBAAAC,sBAAA,CAAtBD,sBAAA,CAAwBS,KAAK,UAAAR,sBAAA,iBAA7BA,sBAAA,CAA+BS,KAAK,GAAI,GAAG,CACzC,CAAC,EACP,CAAC,CACN,CAAC,cACLlD,IAAA,OAAAwB,QAAA,cACItB,KAAA,QAAAsB,QAAA,eACIxB,IAAA,QAAAwB,QAAA,CAAM,EAAAkB,qBAAA,CAAAJ,OAAO,CAACa,iBAAiB,UAAAT,qBAAA,iBAAzBA,qBAAA,CAA2BU,SAAS,GAAI,GAAG,CAAM,CAAC,cACxDpD,IAAA,UAAO6B,SAAS,CAAC,YAAY,CAAAL,QAAA,CACxB,EAAAmB,sBAAA,CAAAL,OAAO,CAACa,iBAAiB,UAAAR,sBAAA,kBAAAC,sBAAA,CAAzBD,sBAAA,CAA2BM,KAAK,UAAAL,sBAAA,iBAAhCA,sBAAA,CAAkCM,KAAK,GAAI,GAAG,CAC5C,CAAC,EACP,CAAC,CACN,CAAC,cACLlD,IAAA,OAAAwB,QAAA,cACIxB,IAAA,CAACJ,KAAK,EAAC2B,EAAE,CAAC,SAAS,CAAAC,QAAA,CACd,EAAAqB,mBAAA,CAAAP,OAAO,CAACe,UAAU,UAAAR,mBAAA,iBAAlBA,mBAAA,CAAoBS,IAAI,GAAI,GAAG,CAC7B,CAAC,CACR,CAAC,cACLpD,KAAA,OAAI2B,SAAS,CAAES,OAAO,CAACZ,MAAM,EAAI,CAAC,CAAG,cAAc,CAAG,aAAc,CAAAF,QAAA,EAC/Dc,OAAO,CAACZ,MAAM,EAAI,CAAC,CAAG,GAAG,CAAG,EAAE,CAAED,YAAY,CAACa,OAAO,CAACZ,MAAM,CAAC,EAC7D,CAAC,cACL1B,IAAA,OAAAwB,QAAA,CAAKH,mBAAmB,CAACiB,OAAO,CAACiB,YAAY,CAAC,CAAK,CAAC,cACpDvD,IAAA,OAAAwB,QAAA,cACIxB,IAAA,QAAKwD,KAAK,CAAE,CAAEC,QAAQ,CAAE,OAAO,CAAEC,QAAQ,CAAE,YAAa,CAAE,CAAAlC,QAAA,CACrDc,OAAO,CAACqB,MAAM,EAAI,GAAG,CACrB,CAAC,CACN,CAAC,cACL3D,IAAA,OAAAwB,QAAA,CAAK,GAAI,CAAAoC,IAAI,CAACtB,OAAO,CAACuB,UAAU,CAAC,CAACC,cAAc,CAAC,CAAC,CAAK,CAAC,GAhCnDxB,OAAO,CAACQ,EAiCb,CAAC,EACR,CACJ,CACE,CAAC,EACL,CAAC,CACD,CAAC,CACV,CAAC,CACN,CAAC,CACL,CAAC,EACC,CAAC,CAEpB,CAAC,CAED,cAAe,CAAA3C,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[97],{1072:(e,s,t)=>{t.d(s,{A:()=>c});var r=t(8139),n=t.n(r),a=t(5043),o=t(7852),l=t(579);const i=a.forwardRef((e,s)=>{let{bsPrefix:t,className:r,as:a="div",...i}=e;const c=(0,o.oU)(t,"row"),d=(0,o.gy)(),u=(0,o.Jm)(),h=`${c}-cols`,m=[];return d.forEach(e=>{const s=i[e];let t;delete i[e],null!=s&&"object"===typeof s?({cols:t}=s):t=s;const r=e!==u?`-${e}`:"";null!=t&&m.push(`${h}${r}-${t}`)}),(0,l.jsx)(a,{ref:s,...i,className:n()(r,c,...m)})});i.displayName="Row";const c=i},3083:(e,s,t)=>{t.d(s,{A:()=>H});var r,n=t(8139),a=t.n(n),o=t(3043),l=t(8279),i=t(182),c=t(8260);function d(e){if((!r&&0!==r||e)&&l.A){var s=document.createElement("div");s.style.position="absolute",s.style.top="-9999px",s.style.width="50px",s.style.height="50px",s.style.overflow="scroll",document.body.appendChild(s),r=s.offsetWidth-s.clientWidth,document.body.removeChild(s)}return r}var u=t(5043);var h=t(6618),m=t(8293);function f(e){const s=function(e){const s=(0,u.useRef)(e);return s.current=e,s}(e);(0,u.useEffect)(()=>()=>s.current(),[])}var x=t(4232),p=t(3655),j=t(5675),y=t(8072),g=t(7852),b=t(579);const v=u.forwardRef((e,s)=>{let{className:t,bsPrefix:r,as:n="div",...o}=e;return r=(0,g.oU)(r,"modal-body"),(0,b.jsx)(n,{ref:s,className:a()(t,r),...o})});v.displayName="ModalBody";const A=v;var w=t(1602);const N=u.forwardRef((e,s)=>{let{bsPrefix:t,className:r,contentClassName:n,centered:o,size:l,fullscreen:i,children:c,scrollable:d,...u}=e;t=(0,g.oU)(t,"modal");const h=`${t}-dialog`,m="string"===typeof i?`${t}-fullscreen-${i}`:`${t}-fullscreen`;return(0,b.jsx)("div",{...u,ref:s,className:a()(h,r,l&&`${t}-${l}`,o&&`${h}-centered`,d&&`${h}-scrollable`,i&&m),children:(0,b.jsx)("div",{className:a()(`${t}-content`,n),children:c})})});N.displayName="ModalDialog";const $=N,_=u.forwardRef((e,s)=>{let{className:t,bsPrefix:r,as:n="div",...o}=e;return r=(0,g.oU)(r,"modal-footer"),(0,b.jsx)(n,{ref:s,className:a()(t,r),...o})});_.displayName="ModalFooter";const S=_;var E=t(2258);const C=u.forwardRef((e,s)=>{let{bsPrefix:t,className:r,closeLabel:n="Close",closeButton:o=!1,...l}=e;return t=(0,g.oU)(t,"modal-header"),(0,b.jsx)(E.A,{ref:s,...l,className:a()(r,t),closeLabel:n,closeButton:o})});C.displayName="ModalHeader";const R=C;const k=(0,t(4488).A)("h4"),D=u.forwardRef((e,s)=>{let{className:t,bsPrefix:r,as:n=k,...o}=e;return r=(0,g.oU)(r,"modal-title"),(0,b.jsx)(n,{ref:s,className:a()(t,r),...o})});D.displayName="ModalTitle";const T=D;function B(e){return(0,b.jsx)(y.A,{...e,timeout:null})}function U(e){return(0,b.jsx)(y.A,{...e,timeout:null})}const F=u.forwardRef((e,s)=>{let{bsPrefix:t,className:r,style:n,dialogClassName:y,contentClassName:v,children:A,dialogAs:N=$,"data-bs-theme":_,"aria-labelledby":S,"aria-describedby":E,"aria-label":C,show:R=!1,animation:k=!0,backdrop:D=!0,keyboard:T=!0,onEscapeKeyDown:F,onShow:H,onHide:O,container:P,autoFocus:z=!0,enforceFocus:I=!0,restoreFocus:M=!0,restoreFocusOptions:L,onEntered:W,onExit:q,onExiting:K,onEnter:G,onEntering:J,onExited:Q,backdropClassName:V,manager:X,...Y}=e;const[Z,ee]=(0,u.useState)({}),[se,te]=(0,u.useState)(!1),re=(0,u.useRef)(!1),ne=(0,u.useRef)(!1),ae=(0,u.useRef)(null),[oe,le]=(0,u.useState)(null),ie=(0,m.A)(s,le),ce=(0,h.A)(O),de=(0,g.Wz)();t=(0,g.oU)(t,"modal");const ue=(0,u.useMemo)(()=>({onHide:ce}),[ce]);function he(){return X||(0,j.R)({isRTL:de})}function me(e){if(!l.A)return;const s=he().getScrollbarWidth()>0,t=e.scrollHeight>(0,i.A)(e).documentElement.clientHeight;ee({paddingRight:s&&!t?d():void 0,paddingLeft:!s&&t?d():void 0})}const fe=(0,h.A)(()=>{oe&&me(oe.dialog)});f(()=>{(0,c.A)(window,"resize",fe),null==ae.current||ae.current()});const xe=()=>{re.current=!0},pe=e=>{re.current&&oe&&e.target===oe.dialog&&(ne.current=!0),re.current=!1},je=()=>{te(!0),ae.current=(0,x.A)(oe.dialog,()=>{te(!1)})},ye=e=>{"static"!==D?ne.current||e.target!==e.currentTarget?ne.current=!1:null==O||O():(e=>{e.target===e.currentTarget&&je()})(e)},ge=(0,u.useCallback)(e=>(0,b.jsx)("div",{...e,className:a()(`${t}-backdrop`,V,!k&&"show")}),[k,V,t]),be={...n,...Z};be.display="block";return(0,b.jsx)(w.A.Provider,{value:ue,children:(0,b.jsx)(p.A,{show:R,ref:ie,backdrop:D,container:P,keyboard:!0,autoFocus:z,enforceFocus:I,restoreFocus:M,restoreFocusOptions:L,onEscapeKeyDown:e=>{T?null==F||F(e):(e.preventDefault(),"static"===D&&je())},onShow:H,onHide:O,onEnter:(e,s)=>{e&&me(e),null==G||G(e,s)},onEntering:(e,s)=>{null==J||J(e,s),(0,o.Ay)(window,"resize",fe)},onEntered:W,onExit:e=>{null==ae.current||ae.current(),null==q||q(e)},onExiting:K,onExited:e=>{e&&(e.style.display=""),null==Q||Q(e),(0,c.A)(window,"resize",fe)},manager:he(),transition:k?B:void 0,backdropTransition:k?U:void 0,renderBackdrop:ge,renderDialog:e=>(0,b.jsx)("div",{role:"dialog",...e,style:be,className:a()(r,t,se&&`${t}-static`,!k&&"show"),onClick:D?ye:void 0,onMouseUp:pe,"data-bs-theme":_,"aria-label":C,"aria-labelledby":S,"aria-describedby":E,children:(0,b.jsx)(N,{...Y,onMouseDown:xe,className:y,contentClassName:v,children:A})})})})});F.displayName="Modal";const H=Object.assign(F,{Body:A,Header:R,Title:T,Footer:S,Dialog:$,TRANSITION_DURATION:300,BACKDROP_TRANSITION_DURATION:150})},3097:(e,s,t)=>{t.r(s),t.d(s,{default:()=>p});var r=t(5043),n=t(3519),a=t(1719),o=t(1072),l=t(8602),i=t(8628),c=t(4196),d=t(4282),u=t(3083),h=t(9853),m=t(4312),f=t(4117),x=t(579);const p=()=>{const{t:e}=(0,f.Bd)(),[s,t]=(0,r.useState)([]),[p,j]=(0,r.useState)(!0),[y,g]=(0,r.useState)(!1),[b,v]=(0,r.useState)(null),[A,w]=(0,r.useState)({name:""}),[N,$]=(0,r.useState)(!1),[_,S]=(0,r.useState)(null),[E,C]=(0,r.useState)({show:!1,type:"",message:""});(0,r.useEffect)(()=>{(async()=>{const e=(0,m.b)();if(!e)return;j(!0);const{data:{user:s}}=await e.auth.getUser();if(!s)return void j(!1);const{data:r,error:n}=await e.from("facilities").select("\n                    id,\n                    name,\n                    created_at,\n                    updated_at\n                ").order("created_at",{ascending:!1});n?console.error("Error fetching facilities:",n):t(r),j(!1)})()},[]);return p?(0,x.jsx)("div",{children:e("loading_facilities")}):(0,x.jsxs)(n.A,{children:[E.show&&(0,x.jsx)(a.A,{variant:E.type,dismissible:!0,onClose:()=>{C({show:!1,type:"",message:""})},className:"mb-4",children:E.message}),(0,x.jsx)("h2",{className:"mb-4",children:e("all_facilities")}),(0,x.jsx)(o.A,{children:(0,x.jsx)(l.A,{children:(0,x.jsx)(i.A,{children:(0,x.jsx)(i.A.Body,{children:(0,x.jsxs)(c.A,{striped:!0,bordered:!0,hover:!0,responsive:!0,children:[(0,x.jsx)("thead",{children:(0,x.jsxs)("tr",{children:[(0,x.jsx)("th",{children:"ID"}),(0,x.jsx)("th",{children:e("name")}),(0,x.jsx)("th",{children:e("created_at")}),(0,x.jsx)("th",{children:e("agent")}),(0,x.jsx)("th",{children:e("actions")})]})}),(0,x.jsx)("tbody",{children:0===s.length?(0,x.jsx)("tr",{children:(0,x.jsx)("td",{colSpan:"9",className:"text-center",children:e("no_facilities_available")})}):s.map(s=>(0,x.jsxs)("tr",{children:[(0,x.jsx)("td",{children:s.id}),(0,x.jsx)("td",{children:s.name}),(0,x.jsx)("td",{children:new Date(s.created_at).toLocaleString()}),(0,x.jsx)("td",{children:new Date(s.updated_at).toLocaleString()}),(0,x.jsxs)("td",{children:[(0,x.jsx)(d.A,{variant:"info",size:"sm",className:"me-2",onClick:()=>(e=>{v(e),w({name:e.name||""}),g(!0)})(s),children:e("edit")}),(0,x.jsx)(d.A,{variant:"danger",size:"sm",onClick:()=>(e=>{S(e),$(!0)})(s),children:e("delete")})]})]},s.id))})]})})})})}),(0,x.jsxs)(u.A,{show:y,onHide:()=>g(!1),size:"lg",children:[(0,x.jsx)(u.A.Header,{closeButton:!0,children:(0,x.jsx)(u.A.Title,{children:e("edit_facility")})}),(0,x.jsx)(u.A.Body,{children:(0,x.jsxs)(h.A,{onSubmit:async r=>{r.preventDefault();const n=(0,m.b)();if(n&&b)try{const{error:r}=await n.from("facilities").update({name:A.name,updated_at:(new Date).toISOString()}).eq("id",b.id);if(r)throw r;t(s.map(e=>e.id===b.id?{...e,...A,updated_at:(new Date).toISOString()}:e)),C({show:!0,type:"success",message:e("item_updated_successfully")}),g(!1),v(null)}catch(a){console.error("Error updating facility:",a),C({show:!0,type:"danger",message:e("failed_to_update_item")+": "+a.message})}},children:[(0,x.jsxs)(h.A.Group,{className:"mb-3",children:[(0,x.jsx)(h.A.Label,{children:e("name")}),(0,x.jsx)(h.A.Control,{type:"text",name:"name",value:A.name,onChange:e=>{const{name:s,value:t}=e.target;w(e=>({...e,[s]:t}))},required:!0})]}),(0,x.jsxs)("div",{className:"d-flex justify-content-end",children:[(0,x.jsx)(d.A,{variant:"secondary",className:"me-2",onClick:()=>g(!1),children:e("cancel")}),(0,x.jsx)(d.A,{variant:"primary",type:"submit",children:e("save_changes")})]})]})})]}),(0,x.jsxs)(u.A,{show:N,onHide:()=>$(!1),children:[(0,x.jsx)(u.A.Header,{closeButton:!0,children:(0,x.jsx)(u.A.Title,{children:e("confirm_delete")})}),(0,x.jsxs)(u.A.Body,{children:[(0,x.jsx)("p",{children:e("delete_confirmation")}),_&&(0,x.jsx)("p",{children:(0,x.jsxs)("strong",{children:[e("name"),": ",_.name]})})]}),(0,x.jsxs)(u.A.Footer,{children:[(0,x.jsx)(d.A,{variant:"secondary",onClick:()=>$(!1),children:e("cancel")}),(0,x.jsx)(d.A,{variant:"danger",onClick:async()=>{const r=(0,m.b)();if(r&&_)try{const{error:n}=await r.from("facilities").delete().eq("id",_.id);if(n)throw n;t(s.filter(e=>e.id!==_.id)),C({show:!0,type:"success",message:e("item_deleted_successfully")}),$(!1),S(null)}catch(n){console.error("Error deleting facility:",n),C({show:!0,type:"danger",message:e("failed_to_delete_item")+": "+n.message})}},children:e("confirm")})]})]})]})}},4196:(e,s,t)=>{t.d(s,{A:()=>c});var r=t(8139),n=t.n(r),a=t(5043),o=t(7852),l=t(579);const i=a.forwardRef((e,s)=>{let{bsPrefix:t,className:r,striped:a,bordered:i,borderless:c,hover:d,size:u,variant:h,responsive:m,...f}=e;const x=(0,o.oU)(t,"table"),p=n()(r,x,h&&`${x}-${h}`,u&&`${x}-${u}`,a&&`${x}-${"string"===typeof a?`striped-${a}`:"striped"}`,i&&`${x}-bordered`,c&&`${x}-borderless`,d&&`${x}-hover`),j=(0,l.jsx)("table",{...f,className:p,ref:s});if(m){let e=`${x}-responsive`;return"string"===typeof m&&(e=`${e}-${m}`),(0,l.jsx)("div",{className:e,children:j})}return j});i.displayName="Table";const c=i}}]);
//# sourceMappingURL=97.249411ac.chunk.js.map
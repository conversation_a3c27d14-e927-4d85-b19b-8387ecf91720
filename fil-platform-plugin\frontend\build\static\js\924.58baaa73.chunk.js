"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[924],{1072:(e,s,t)=>{t.d(s,{A:()=>c});var r=t(8139),n=t.n(r),i=t(5043),a=t(7852),o=t(579);const l=i.forwardRef((e,s)=>{let{bsPrefix:t,className:r,as:i="div",...l}=e;const c=(0,a.oU)(t,"row"),d=(0,a.gy)(),u=(0,a.Jm)(),m=`${c}-cols`,f=[];return d.forEach(e=>{const s=l[e];let t;delete l[e],null!=s&&"object"===typeof s?({cols:t}=s):t=s;const r=e!==u?`-${e}`:"";null!=t&&f.push(`${m}${r}-${t}`)}),(0,o.jsx)(i,{ref:s,...l,className:n()(r,c,...f)})});l.displayName="Row";const c=l},2924:(e,s,t)=>{t.r(s),t.d(s,{default:()=>j});var r=t(5043),n=t(3519),i=t(1719),a=t(1072),o=t(8602),l=t(8628),c=t(4196),d=t(4282),u=t(3083),m=t(9853),f=t(4312),h=t(4117),x=t(579);const j=()=>{const{t:e}=(0,h.Bd)(),[s,t]=(0,r.useState)([]),[j,p]=(0,r.useState)(!0),[g,y]=(0,r.useState)(!1),[_,b]=(0,r.useState)(null),[v,A]=(0,r.useState)({category:"",filecoin_miner_id:"",sector_size:"",effective_until:""}),[N,w]=(0,r.useState)(!1),[$,C]=(0,r.useState)(null),[S,E]=(0,r.useState)({show:!1,type:"",message:""});(0,r.useEffect)(()=>{(async()=>{const e=(0,f.b)();if(!e)return;p(!0);const{data:{user:s}}=await e.auth.getUser();if(!s)return void p(!1);const{data:r,error:n}=await e.from("miners").select("\n                    id,\n                    category,\n                    filecoin_miner_id,\n                    sector_size,\n                    effective_until,\n                    created_at,\n                    updated_at,\n                    facilities (\n                        name\n                    )\n                ").order("created_at",{ascending:!1});n?console.error("Error fetching miners:",n):t(r),p(!1)})()},[]);const R=e=>{const{name:s,value:t}=e.target;A(e=>({...e,[s]:t}))};return j?(0,x.jsx)("div",{children:e("loading_miners")}):(0,x.jsxs)(n.A,{children:[S.show&&(0,x.jsx)(i.A,{variant:S.type,dismissible:!0,onClose:()=>{E({show:!1,type:"",message:""})},className:"mb-4",children:S.message}),(0,x.jsx)("h2",{className:"mb-4",children:e("all_miners")}),(0,x.jsx)(a.A,{children:(0,x.jsx)(o.A,{children:(0,x.jsx)(l.A,{children:(0,x.jsx)(l.A.Body,{children:(0,x.jsxs)(c.A,{striped:!0,bordered:!0,hover:!0,responsive:!0,children:[(0,x.jsx)("thead",{children:(0,x.jsxs)("tr",{children:[(0,x.jsx)("th",{children:"ID"}),(0,x.jsx)("th",{children:e("category")}),(0,x.jsx)("th",{children:e("facility")}),(0,x.jsx)("th",{children:e("miner_id")}),(0,x.jsx)("th",{children:e("effective_until")}),(0,x.jsx)("th",{children:e("created_at")}),(0,x.jsx)("th",{children:e("updated_at")}),(0,x.jsx)("th",{children:e("actions")})]})}),(0,x.jsx)("tbody",{children:0===s.length?(0,x.jsx)("tr",{children:(0,x.jsx)("td",{colSpan:"9",className:"text-center",children:e("no_miners_available")})}):s.map(s=>{var t;return(0,x.jsxs)("tr",{children:[(0,x.jsxs)("td",{children:[s.id.substring(0,8),"..."]}),(0,x.jsx)("td",{children:s.category}),(0,x.jsx)("td",{children:(null===(t=s.facilities)||void 0===t?void 0:t.name)||"-"}),(0,x.jsx)("td",{children:s.filecoin_miner_id}),(0,x.jsx)("td",{children:s.sector_size}),(0,x.jsx)("td",{children:s.effective_until}),(0,x.jsx)("td",{children:new Date(s.created_at).toLocaleString()}),(0,x.jsx)("td",{children:new Date(s.updated_at).toLocaleString()}),(0,x.jsxs)("td",{children:[(0,x.jsx)(d.A,{variant:"info",size:"sm",className:"me-2",onClick:()=>(e=>{b(e),A({category:e.category||"",filecoin_miner_id:e.filecoin_miner_id||"",sector_size:e.sector_size||"",effective_until:e.effective_until||""}),y(!0)})(s),children:e("edit")}),(0,x.jsx)(d.A,{variant:"danger",size:"sm",onClick:()=>(e=>{C(e),w(!0)})(s),children:e("delete")})]})]},s.id)})})]})})})})}),(0,x.jsxs)(u.A,{show:g,onHide:()=>y(!1),size:"lg",children:[(0,x.jsx)(u.A.Header,{closeButton:!0,children:(0,x.jsx)(u.A.Title,{children:e("edit_miner")})}),(0,x.jsx)(u.A.Body,{children:(0,x.jsxs)(m.A,{onSubmit:async r=>{r.preventDefault();const n=(0,f.b)();if(n&&_)try{const{error:r}=await n.from("miners").update({category:v.category,filecoin_miner_id:v.filecoin_miner_id,sector_size:v.sector_size,effective_until:v.effective_until,updated_at:(new Date).toISOString()}).eq("id",_.id);if(r)throw r;t(s.map(e=>e.id===_.id?{...e,...v,updated_at:(new Date).toISOString()}:e)),E({show:!0,type:"success",message:e("item_updated_successfully")}),y(!1),b(null)}catch(i){console.error("Error updating miner:",i),E({show:!0,type:"danger",message:e("failed_to_update_item")+": "+i.message})}},children:[(0,x.jsxs)(m.A.Group,{className:"mb-3",children:[(0,x.jsx)(m.A.Label,{children:e("category")}),(0,x.jsx)(m.A.Control,{type:"text",name:"category",value:v.category,onChange:R,required:!0})]}),(0,x.jsxs)(m.A.Group,{className:"mb-3",children:[(0,x.jsx)(m.A.Label,{children:e("miner_id")}),(0,x.jsx)(m.A.Control,{type:"text",name:"filecoin_miner_id",value:v.filecoin_miner_id,onChange:R,required:!0})]}),(0,x.jsxs)(m.A.Group,{className:"mb-3",children:[(0,x.jsx)(m.A.Label,{children:e("sector_size")}),(0,x.jsx)(m.A.Control,{type:"text",name:"sector_size",value:v.sector_size,onChange:R,required:!0})]}),(0,x.jsxs)(m.A.Group,{className:"mb-3",children:[(0,x.jsx)(m.A.Label,{children:e("effective_until")}),(0,x.jsx)(m.A.Control,{type:"date",name:"effective_until",value:v.effective_until,onChange:R,required:!0})]}),(0,x.jsxs)("div",{className:"d-flex justify-content-end",children:[(0,x.jsx)(d.A,{variant:"secondary",className:"me-2",onClick:()=>y(!1),children:e("cancel")}),(0,x.jsx)(d.A,{variant:"primary",type:"submit",children:e("save_changes")})]})]})})]}),(0,x.jsxs)(u.A,{show:N,onHide:()=>w(!1),children:[(0,x.jsx)(u.A.Header,{closeButton:!0,children:(0,x.jsx)(u.A.Title,{children:e("confirm_delete")})}),(0,x.jsxs)(u.A.Body,{children:[(0,x.jsx)("p",{children:e("delete_confirmation")}),$&&(0,x.jsx)("p",{children:(0,x.jsxs)("strong",{children:[e("miner_id"),": ",$.filecoin_miner_id]})})]}),(0,x.jsxs)(u.A.Footer,{children:[(0,x.jsx)(d.A,{variant:"secondary",onClick:()=>w(!1),children:e("cancel")}),(0,x.jsx)(d.A,{variant:"danger",onClick:async()=>{const r=(0,f.b)();if(r&&$)try{const{error:n}=await r.from("miners").delete().eq("id",$.id);if(n)throw n;t(s.filter(e=>e.id!==$.id)),E({show:!0,type:"success",message:e("item_deleted_successfully")}),w(!1),C(null)}catch(n){console.error("Error deleting miner:",n),E({show:!0,type:"danger",message:e("failed_to_delete_item")+": "+n.message})}},children:e("confirm")})]})]})]})}},3083:(e,s,t)=>{t.d(s,{A:()=>F});var r,n=t(8139),i=t.n(n),a=t(3043),o=t(8279),l=t(182),c=t(8260);function d(e){if((!r&&0!==r||e)&&o.A){var s=document.createElement("div");s.style.position="absolute",s.style.top="-9999px",s.style.width="50px",s.style.height="50px",s.style.overflow="scroll",document.body.appendChild(s),r=s.offsetWidth-s.clientWidth,document.body.removeChild(s)}return r}var u=t(5043);var m=t(6618),f=t(8293);function h(e){const s=function(e){const s=(0,u.useRef)(e);return s.current=e,s}(e);(0,u.useEffect)(()=>()=>s.current(),[])}var x=t(4232),j=t(3655),p=t(5675),g=t(8072),y=t(7852),_=t(579);const b=u.forwardRef((e,s)=>{let{className:t,bsPrefix:r,as:n="div",...a}=e;return r=(0,y.oU)(r,"modal-body"),(0,_.jsx)(n,{ref:s,className:i()(t,r),...a})});b.displayName="ModalBody";const v=b;var A=t(1602);const N=u.forwardRef((e,s)=>{let{bsPrefix:t,className:r,contentClassName:n,centered:a,size:o,fullscreen:l,children:c,scrollable:d,...u}=e;t=(0,y.oU)(t,"modal");const m=`${t}-dialog`,f="string"===typeof l?`${t}-fullscreen-${l}`:`${t}-fullscreen`;return(0,_.jsx)("div",{...u,ref:s,className:i()(m,r,o&&`${t}-${o}`,a&&`${m}-centered`,d&&`${m}-scrollable`,l&&f),children:(0,_.jsx)("div",{className:i()(`${t}-content`,n),children:c})})});N.displayName="ModalDialog";const w=N,$=u.forwardRef((e,s)=>{let{className:t,bsPrefix:r,as:n="div",...a}=e;return r=(0,y.oU)(r,"modal-footer"),(0,_.jsx)(n,{ref:s,className:i()(t,r),...a})});$.displayName="ModalFooter";const C=$;var S=t(2258);const E=u.forwardRef((e,s)=>{let{bsPrefix:t,className:r,closeLabel:n="Close",closeButton:a=!1,...o}=e;return t=(0,y.oU)(t,"modal-header"),(0,_.jsx)(S.A,{ref:s,...o,className:i()(r,t),closeLabel:n,closeButton:a})});E.displayName="ModalHeader";const R=E;const k=(0,t(4488).A)("h4"),z=u.forwardRef((e,s)=>{let{className:t,bsPrefix:r,as:n=k,...a}=e;return r=(0,y.oU)(r,"modal-title"),(0,_.jsx)(n,{ref:s,className:i()(t,r),...a})});z.displayName="ModalTitle";const D=z;function T(e){return(0,_.jsx)(g.A,{...e,timeout:null})}function B(e){return(0,_.jsx)(g.A,{...e,timeout:null})}const U=u.forwardRef((e,s)=>{let{bsPrefix:t,className:r,style:n,dialogClassName:g,contentClassName:b,children:v,dialogAs:N=w,"data-bs-theme":$,"aria-labelledby":C,"aria-describedby":S,"aria-label":E,show:R=!1,animation:k=!0,backdrop:z=!0,keyboard:D=!0,onEscapeKeyDown:U,onShow:F,onHide:H,container:L,autoFocus:O=!0,enforceFocus:P=!0,restoreFocus:I=!0,restoreFocusOptions:M,onEntered:q,onExit:G,onExiting:W,onEnter:K,onEntering:J,onExited:Q,backdropClassName:V,manager:X,...Y}=e;const[Z,ee]=(0,u.useState)({}),[se,te]=(0,u.useState)(!1),re=(0,u.useRef)(!1),ne=(0,u.useRef)(!1),ie=(0,u.useRef)(null),[ae,oe]=(0,u.useState)(null),le=(0,f.A)(s,oe),ce=(0,m.A)(H),de=(0,y.Wz)();t=(0,y.oU)(t,"modal");const ue=(0,u.useMemo)(()=>({onHide:ce}),[ce]);function me(){return X||(0,p.R)({isRTL:de})}function fe(e){if(!o.A)return;const s=me().getScrollbarWidth()>0,t=e.scrollHeight>(0,l.A)(e).documentElement.clientHeight;ee({paddingRight:s&&!t?d():void 0,paddingLeft:!s&&t?d():void 0})}const he=(0,m.A)(()=>{ae&&fe(ae.dialog)});h(()=>{(0,c.A)(window,"resize",he),null==ie.current||ie.current()});const xe=()=>{re.current=!0},je=e=>{re.current&&ae&&e.target===ae.dialog&&(ne.current=!0),re.current=!1},pe=()=>{te(!0),ie.current=(0,x.A)(ae.dialog,()=>{te(!1)})},ge=e=>{"static"!==z?ne.current||e.target!==e.currentTarget?ne.current=!1:null==H||H():(e=>{e.target===e.currentTarget&&pe()})(e)},ye=(0,u.useCallback)(e=>(0,_.jsx)("div",{...e,className:i()(`${t}-backdrop`,V,!k&&"show")}),[k,V,t]),_e={...n,...Z};_e.display="block";return(0,_.jsx)(A.A.Provider,{value:ue,children:(0,_.jsx)(j.A,{show:R,ref:le,backdrop:z,container:L,keyboard:!0,autoFocus:O,enforceFocus:P,restoreFocus:I,restoreFocusOptions:M,onEscapeKeyDown:e=>{D?null==U||U(e):(e.preventDefault(),"static"===z&&pe())},onShow:F,onHide:H,onEnter:(e,s)=>{e&&fe(e),null==K||K(e,s)},onEntering:(e,s)=>{null==J||J(e,s),(0,a.Ay)(window,"resize",he)},onEntered:q,onExit:e=>{null==ie.current||ie.current(),null==G||G(e)},onExiting:W,onExited:e=>{e&&(e.style.display=""),null==Q||Q(e),(0,c.A)(window,"resize",he)},manager:me(),transition:k?T:void 0,backdropTransition:k?B:void 0,renderBackdrop:ye,renderDialog:e=>(0,_.jsx)("div",{role:"dialog",...e,style:_e,className:i()(r,t,se&&`${t}-static`,!k&&"show"),onClick:z?ge:void 0,onMouseUp:je,"data-bs-theme":$,"aria-label":E,"aria-labelledby":C,"aria-describedby":S,children:(0,_.jsx)(N,{...Y,onMouseDown:xe,className:g,contentClassName:b,children:v})})})})});U.displayName="Modal";const F=Object.assign(U,{Body:v,Header:R,Title:D,Footer:C,Dialog:w,TRANSITION_DURATION:300,BACKDROP_TRANSITION_DURATION:150})},4196:(e,s,t)=>{t.d(s,{A:()=>c});var r=t(8139),n=t.n(r),i=t(5043),a=t(7852),o=t(579);const l=i.forwardRef((e,s)=>{let{bsPrefix:t,className:r,striped:i,bordered:l,borderless:c,hover:d,size:u,variant:m,responsive:f,...h}=e;const x=(0,a.oU)(t,"table"),j=n()(r,x,m&&`${x}-${m}`,u&&`${x}-${u}`,i&&`${x}-${"string"===typeof i?`striped-${i}`:"striped"}`,l&&`${x}-bordered`,c&&`${x}-borderless`,d&&`${x}-hover`),p=(0,o.jsx)("table",{...h,className:j,ref:s});if(f){let e=`${x}-responsive`;return"string"===typeof f&&(e=`${e}-${f}`),(0,o.jsx)("div",{className:e,children:p})}return p});l.displayName="Table";const c=l}}]);
//# sourceMappingURL=924.58baaa73.chunk.js.map
-- 检查用户是否存在
SELECT id, email, role FROM users WHERE id = 'ee66690f-d3eb-49d9-afa5-77ef9d0cdaab';

-- 检查是否已有agent_profiles记录
SELECT * FROM agent_profiles WHERE user_id = 'ee66690f-d3eb-49d9-afa5-77ef9d0cdaab';

-- 如果没有agent_profiles记录，创建一个
INSERT INTO agent_profiles (user_id, brand_name, commission_pct, kyc_status)
VALUES ('ee66690f-d3eb-49d9-afa5-77ef9d0cdaab', 'Default Agent', 0.05, 'pending')
ON CONFLICT (user_id) DO NOTHING;

-- 验证创建结果
SELECT * FROM agent_profiles WHERE user_id = 'ee66690f-d3eb-49d9-afa5-77ef9d0cdaab';
